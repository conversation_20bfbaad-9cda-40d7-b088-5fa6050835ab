<template>
  <div class="canvas-wrapper" ref="canvasWrapper">
    <v-stage
      ref="stage"
      :config="stageConfig"
      @mousedown="handleStageMouseDown"
      @mousemove="handleStageMouseMove"
      @mouseup="handleStageMouseUp"
      @dragover="handleDragOver"
      @drop="handleDrop"
    >
      <v-layer ref="layer">
        <!-- 渲染组件 -->
        <v-group
          v-for="component in components"
          :key="component.id"
          :config="{
            x: component.x,
            y: component.y,
            draggable: true
          }"
          @dragend="handleComponentDragEnd(component, $event)"
          @click="handleComponentClick(component, $event)"
          @dblclick="handleComponentDoubleClick(component)"
        >
          <!-- 矩形组件 -->
          <v-rect
            v-if="component.type === 'rectangle'"
            :config="{
              width: component.width,
              height: component.height,
              fill: component.color,
              stroke: component.selected ? '#409EFF' : 'transparent',
              strokeWidth: component.selected ? 2 : 0,
              cornerRadius: 4
            }"
          />
          
          <!-- 圆形组件 -->
          <v-circle
            v-if="component.type === 'circle'"
            :config="{
              radius: component.width / 2,
              fill: component.color,
              stroke: component.selected ? '#409EFF' : 'transparent',
              strokeWidth: component.selected ? 2 : 0
            }"
          />
          
          <!-- 组件文本 -->
          <v-text
            :config="{
              text: component.name,
              fontSize: 12,
              fill: 'white',
              width: component.width,
              height: component.height,
              align: 'center',
              verticalAlign: 'middle'
            }"
          />
        </v-group>

        <!-- 渲染线条 -->
        <v-group v-for="line in lines" :key="line.id">
          <!-- 直线 -->
          <v-line
            v-if="line.type === 'line'"
            :config="{
              points: [line.startX, line.startY, line.endX, line.endY],
              stroke: line.color,
              strokeWidth: 2
            }"
          />

          <!-- 箭头线 -->
          <v-arrow
            v-if="line.type === 'arrow'"
            :config="{
              points: [line.startX, line.startY, line.endX, line.endY],
              stroke: line.color,
              fill: line.color,
              strokeWidth: 2,
              pointerLength: 10,
              pointerWidth: 8
            }"
          />

          <!-- 双箭头线 -->
          <template v-if="line.type === 'double-arrow'">
            <v-line
              :config="{
                points: [line.startX, line.startY, line.endX, line.endY],
                stroke: line.color,
                strokeWidth: 2
              }"
            />
            <!-- 起始箭头 -->
            <v-arrow
              :config="{
                points: [line.startX + 10, line.startY, line.startX, line.startY],
                stroke: line.color,
                fill: line.color,
                strokeWidth: 2,
                pointerLength: 10,
                pointerWidth: 8
              }"
            />
            <!-- 结束箭头 -->
            <v-arrow
              :config="{
                points: [line.endX - 10, line.endY, line.endX, line.endY],
                stroke: line.color,
                fill: line.color,
                strokeWidth: 2,
                pointerLength: 10,
                pointerWidth: 8
              }"
            />
          </template>

          <!-- 曲线 -->
          <v-line
            v-if="line.type === 'curve'"
            :config="{
              points: line.points || [line.startX, line.startY, line.endX, line.endY],
              stroke: line.color,
              strokeWidth: 2,
              tension: 0.5
            }"
          />
        </v-group>

        <!-- 正在绘制的线条 -->
        <v-group v-if="currentLine">
          <!-- 直线 -->
          <v-line
            v-if="currentLine.type === 'line'"
            :config="{
              points: [currentLine.startX, currentLine.startY, currentLine.endX, currentLine.endY],
              stroke: currentLine.color,
              strokeWidth: 2,
              dash: [5, 5]
            }"
          />

          <!-- 箭头线 -->
          <v-arrow
            v-if="currentLine.type === 'arrow'"
            :config="{
              points: [currentLine.startX, currentLine.startY, currentLine.endX, currentLine.endY],
              stroke: currentLine.color,
              fill: currentLine.color,
              strokeWidth: 2,
              pointerLength: 10,
              pointerWidth: 8,
              dash: [5, 5]
            }"
          />

          <!-- 双箭头线 -->
          <template v-if="currentLine.type === 'double-arrow'">
            <v-line
              :config="{
                points: [currentLine.startX, currentLine.startY, currentLine.endX, currentLine.endY],
                stroke: currentLine.color,
                strokeWidth: 2,
                dash: [5, 5]
              }"
            />
          </template>

          <!-- 曲线 -->
          <v-line
            v-if="currentLine.type === 'curve'"
            :config="{
              points: currentLine.points || [currentLine.startX, currentLine.startY, currentLine.endX, currentLine.endY],
              stroke: currentLine.color,
              strokeWidth: 2,
              tension: 0.5,
              dash: [5, 5]
            }"
          />
        </v-group>

        <!-- 选择框 -->
        <v-rect
          v-if="selectionBox.visible"
          :config="{
            x: selectionBox.x,
            y: selectionBox.y,
            width: selectionBox.width,
            height: selectionBox.height,
            fill: 'rgba(64, 158, 255, 0.1)',
            stroke: '#409EFF',
            strokeWidth: 1,
            dash: [5, 5]
          }"
        />

        <!-- 对齐辅助线 -->
        <v-line
          v-for="guide in alignmentGuides"
          :key="guide.id"
          :config="{
            points: guide.points,
            stroke: '#ff0000',
            strokeWidth: 1,
            dash: [5, 5]
          }"
        />
      </v-layer>
    </v-stage>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, computed, onMounted, nextTick } from 'vue'
import type { CanvasComponent, CanvasLine, CanvasData, ComponentItem } from '../types/canvas'

// Props
const props = defineProps<{
  selectedLineType: string
  lineColor: string
}>()

// Emits
const emit = defineEmits<{
  componentSelected: [component: CanvasComponent | null]
  componentDoubleClick: [component: CanvasComponent]
}>()

// 响应式数据
const canvasWrapper = ref()
const stage = ref()
const layer = ref()

const components = ref<CanvasComponent[]>([])
const lines = ref<CanvasLine[]>([])
const selectedComponents = ref<string[]>([])
const alignmentGuides = ref<any[]>([])

const stageConfig = reactive({
  width: 800,
  height: 600
})

const selectionBox = reactive({
  visible: false,
  x: 0,
  y: 0,
  width: 0,
  height: 0
})

const isDrawingLine = ref(false)
const currentLine = ref<Partial<CanvasLine> | null>(null)
const isDragging = ref(false)
const dragStartPos = reactive({ x: 0, y: 0 })

// 计算属性
const selectedComponentObjects = computed(() => {
  return components.value.filter(comp => selectedComponents.value.includes(comp.id))
})

// 方法
const updateStageSize = () => {
  if (canvasWrapper.value) {
    const rect = canvasWrapper.value.getBoundingClientRect()
    stageConfig.width = rect.width
    stageConfig.height = rect.height
  }
}

const handleDragOver = (event: DragEvent) => {
  event.preventDefault()
}

const handleDrop = (event: DragEvent) => {
  event.preventDefault()
  
  const data = event.dataTransfer?.getData('application/json')
  if (data) {
    const componentData: ComponentItem = JSON.parse(data)
    const stagePos = stage.value?.getNode().getPointerPosition()
    
    if (stagePos) {
      const newComponent: CanvasComponent = {
        ...componentData,
        id: `comp_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`,
        x: stagePos.x - componentData.width / 2,
        y: stagePos.y - componentData.height / 2,
        selected: false
      }
      
      components.value.push(newComponent)
    }
  }
}

const handleStageMouseDown = (event: any) => {
  const clickedOnEmpty = event.target === event.target.getStage()

  if (clickedOnEmpty) {
    const pos = stage.value?.getNode().getPointerPosition()

    // 检查是否在绘制线条模式
    if (props.selectedLineType && props.selectedLineType !== 'none' && pos) {
      if (!isDrawingLine.value) {
        // 开始绘制线条
        isDrawingLine.value = true
        currentLine.value = {
          id: `line_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`,
          type: props.selectedLineType as any,
          startX: pos.x,
          startY: pos.y,
          endX: pos.x,
          endY: pos.y,
          color: props.lineColor,
          points: props.selectedLineType === 'curve' ? [pos.x, pos.y, pos.x, pos.y] : undefined
        }
      } else {
        // 完成线条绘制
        if (currentLine.value) {
          currentLine.value.endX = pos.x
          currentLine.value.endY = pos.y

          if (currentLine.value.type === 'curve' && currentLine.value.points) {
            // 为曲线添加控制点
            const midX = (currentLine.value.startX! + pos.x) / 2
            const midY = (currentLine.value.startY! + pos.y) / 2 - 50
            currentLine.value.points = [
              currentLine.value.startX!,
              currentLine.value.startY!,
              midX,
              midY,
              pos.x,
              pos.y
            ]
          }

          lines.value.push(currentLine.value as CanvasLine)
          currentLine.value = null
        }
        isDrawingLine.value = false
      }
      return
    }

    // 清除选择
    clearSelection()

    // 开始选择框
    if (pos) {
      dragStartPos.x = pos.x
      dragStartPos.y = pos.y
      selectionBox.x = pos.x
      selectionBox.y = pos.y
      selectionBox.width = 0
      selectionBox.height = 0
      selectionBox.visible = true
      isDragging.value = true
    }
  }
}

const handleStageMouseMove = (event: any) => {
  const pos = stage.value?.getNode().getPointerPosition()

  if (isDrawingLine.value && currentLine.value && pos) {
    // 更新正在绘制的线条
    currentLine.value.endX = pos.x
    currentLine.value.endY = pos.y

    if (currentLine.value.type === 'curve' && currentLine.value.points) {
      const midX = (currentLine.value.startX! + pos.x) / 2
      const midY = (currentLine.value.startY! + pos.y) / 2 - 50
      currentLine.value.points = [
        currentLine.value.startX!,
        currentLine.value.startY!,
        midX,
        midY,
        pos.x,
        pos.y
      ]
    }
  } else if (isDragging.value && pos) {
    selectionBox.width = pos.x - dragStartPos.x
    selectionBox.height = pos.y - dragStartPos.y
  }
}

const handleStageMouseUp = () => {
  if (isDragging.value) {
    // 选择框内的组件
    if (Math.abs(selectionBox.width) > 5 || Math.abs(selectionBox.height) > 5) {
      selectComponentsInBox()
    }
    
    selectionBox.visible = false
    isDragging.value = false
  }
}

const selectComponentsInBox = () => {
  const box = {
    x: Math.min(selectionBox.x, selectionBox.x + selectionBox.width),
    y: Math.min(selectionBox.y, selectionBox.y + selectionBox.height),
    width: Math.abs(selectionBox.width),
    height: Math.abs(selectionBox.height)
  }
  
  const selected: string[] = []
  components.value.forEach(comp => {
    if (comp.x >= box.x && comp.y >= box.y && 
        comp.x + comp.width <= box.x + box.width && 
        comp.y + comp.height <= box.y + box.height) {
      selected.push(comp.id)
    }
  })
  
  selectedComponents.value = selected
  updateComponentSelection()
}

const handleComponentClick = (component: CanvasComponent, event: any) => {
  event.cancelBubble = true
  
  if (event.evt.ctrlKey || event.evt.metaKey) {
    // 多选
    if (selectedComponents.value.includes(component.id)) {
      selectedComponents.value = selectedComponents.value.filter(id => id !== component.id)
    } else {
      selectedComponents.value.push(component.id)
    }
  } else {
    // 单选
    selectedComponents.value = [component.id]
  }
  
  updateComponentSelection()
  emit('componentSelected', component)
}

const handleComponentDoubleClick = (component: CanvasComponent) => {
  // 双击事件 - 进入二级画布（加分项）
  emit('componentDoubleClick', component)
}

const handleComponentDragEnd = (component: CanvasComponent, event: any) => {
  const newPos = event.target.position()
  component.x = newPos.x
  component.y = newPos.y
  
  // 显示对齐辅助线
  showAlignmentGuides(component)
  
  // 延迟隐藏辅助线
  setTimeout(() => {
    alignmentGuides.value = []
  }, 1000)
}

const showAlignmentGuides = (draggedComponent: CanvasComponent) => {
  alignmentGuides.value = []
  const threshold = 5
  
  components.value.forEach(comp => {
    if (comp.id === draggedComponent.id) return
    
    // 垂直对齐
    if (Math.abs(comp.x - draggedComponent.x) < threshold) {
      alignmentGuides.value.push({
        id: `v_${comp.id}`,
        points: [comp.x, 0, comp.x, stageConfig.height]
      })
      draggedComponent.x = comp.x
    }
    
    // 水平对齐
    if (Math.abs(comp.y - draggedComponent.y) < threshold) {
      alignmentGuides.value.push({
        id: `h_${comp.id}`,
        points: [0, comp.y, stageConfig.width, comp.y]
      })
      draggedComponent.y = comp.y
    }
  })
}

const clearSelection = () => {
  selectedComponents.value = []
  updateComponentSelection()
}

const updateComponentSelection = () => {
  components.value.forEach(comp => {
    comp.selected = selectedComponents.value.includes(comp.id)
  })
}

// 公开方法
const getCanvasData = (): CanvasData => {
  return {
    components: components.value.map(comp => ({ ...comp, selected: false })),
    lines: lines.value
  }
}

const loadCanvasData = (data: CanvasData) => {
  components.value = data.components || []
  lines.value = data.lines || []
  clearSelection()
}

const copySelected = () => {
  if (selectedComponents.value.length > 0) {
    const selectedComps = selectedComponentObjects.value
    selectedComps.forEach(comp => {
      const newComp: CanvasComponent = {
        ...comp,
        id: `comp_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`,
        x: comp.x + 20,
        y: comp.y + 20,
        selected: false
      }
      components.value.push(newComp)
    })
  }
}

const deleteSelected = () => {
  components.value = components.value.filter(comp => !selectedComponents.value.includes(comp.id))
  selectedComponents.value = []
}

const alignSelected = (direction: 'left' | 'center' | 'right') => {
  if (selectedComponents.value.length < 2) return
  
  const selected = selectedComponentObjects.value
  
  switch (direction) {
    case 'left':
      const leftMost = Math.min(...selected.map(comp => comp.x))
      selected.forEach(comp => comp.x = leftMost)
      break
    case 'center':
      const centerX = selected.reduce((sum, comp) => sum + comp.x + comp.width / 2, 0) / selected.length
      selected.forEach(comp => comp.x = centerX - comp.width / 2)
      break
    case 'right':
      const rightMost = Math.max(...selected.map(comp => comp.x + comp.width))
      selected.forEach(comp => comp.x = rightMost - comp.width)
      break
  }
}

// 暴露方法给父组件
defineExpose({
  getCanvasData,
  loadCanvasData,
  copySelected,
  deleteSelected,
  alignSelected
})

onMounted(() => {
  updateStageSize()
  window.addEventListener('resize', updateStageSize)
})
</script>

<style scoped>
.canvas-wrapper {
  width: 100%;
  height: 100%;
  background: white;
  position: relative;
  overflow: hidden;
}
</style>
