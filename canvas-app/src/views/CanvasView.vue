<template>
  <div class="canvas-container">
    <!-- 顶部工具栏 -->
    <div class="top-toolbar">
      <div class="line-tools">
        <el-button-group>
          <el-button
            :type="selectedLineType === 'none' ? 'primary' : 'default'"
            @click="selectLineType('none')"
            size="small"
          >
            <el-icon><Pointer /></el-icon>
            选择
          </el-button>
          <el-button
            :type="selectedLineType === 'arrow' ? 'primary' : 'default'"
            @click="selectLineType('arrow')"
            size="small"
          >
            <el-icon><Right /></el-icon>
            单箭头
          </el-button>
          <el-button
            :type="selectedLineType === 'double-arrow' ? 'primary' : 'default'"
            @click="selectLineType('double-arrow')"
            size="small"
          >
            <el-icon><Sort /></el-icon>
            双箭头
          </el-button>
          <el-button
            :type="selectedLineType === 'line' ? 'primary' : 'default'"
            @click="selectLineType('line')"
            size="small"
          >
            <el-icon><Minus /></el-icon>
            直线
          </el-button>
          <el-button
            :type="selectedLineType === 'curve' ? 'primary' : 'default'"
            @click="selectLineType('curve')"
            size="small"
          >
            <el-icon><Connection /></el-icon>
            曲线
          </el-button>
        </el-button-group>

        <el-color-picker
          v-model="lineColor"
          size="small"
          style="margin-left: 10px;"
        />
      </div>
      
      <div class="save-tools">
        <el-button @click="saveCanvas" type="primary" size="small">
          <el-icon><Download /></el-icon>
          保存
        </el-button>
        <el-button @click="loadCanvas" size="small">
          <el-icon><Upload /></el-icon>
          导入
        </el-button>
      </div>
    </div>

    <div class="main-content">
      <!-- 左侧组件面板 -->
      <div class="left-panel">
        <div class="panel-title">组件库</div>
        <div class="component-list">
          <div 
            v-for="component in componentLibrary" 
            :key="component.id"
            class="component-item"
            :draggable="true"
            @dragstart="onDragStart($event, component)"
          >
            <div class="component-preview" :style="getComponentStyle(component)">
              {{ component.name }}
            </div>
          </div>
        </div>
      </div>

      <!-- 中间画布区域 -->
      <div class="canvas-area">
        <CanvasComponent 
          ref="canvasRef"
          :selected-line-type="selectedLineType"
          :line-color="lineColor"
          @component-selected="onComponentSelected"
        />
      </div>
    </div>

    <!-- 底部状态栏 -->
    <div class="status-bar">
      <div class="status-left">
        <span class="status-item">
          模式: {{ getModeText() }}
        </span>
        <span class="status-item" v-if="selectedLineType !== 'none'">
          {{ getLineTypeHint() }}
        </span>
      </div>
      <div class="status-right">
        <span class="status-item">
          快捷键: Ctrl+S保存 | Ctrl+C复制 | Delete删除 | Esc取消
        </span>
      </div>
    </div>

    <!-- 右键菜单 -->
    <el-dropdown
      ref="contextMenuRef"
      trigger="contextmenu"
      :teleported="false"
      style="position: absolute; z-index: 9999;"
      :style="{ left: contextMenuPosition.x + 'px', top: contextMenuPosition.y + 'px' }"
      v-show="showContextMenu"
    >
      <span></span>
      <template #dropdown>
        <el-dropdown-menu>
          <el-dropdown-item @click="copySelected">
            <el-icon><CopyDocument /></el-icon>
            复制
          </el-dropdown-item>
          <el-dropdown-item @click="deleteSelected">
            <el-icon><Delete /></el-icon>
            删除
          </el-dropdown-item>
          <el-dropdown-item divided @click="alignLeft">
            <el-icon><AlignLeft /></el-icon>
            左对齐
          </el-dropdown-item>
          <el-dropdown-item @click="alignCenter">
            <el-icon><AlignCenter /></el-icon>
            居中对齐
          </el-dropdown-item>
          <el-dropdown-item @click="alignRight">
            <el-icon><AlignRight /></el-icon>
            右对齐
          </el-dropdown-item>
        </el-dropdown-menu>
      </template>
    </el-dropdown>

    <!-- 文件输入 -->
    <input 
      ref="fileInputRef" 
      type="file" 
      accept=".json" 
      style="display: none;" 
      @change="onFileSelected"
    />
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, onMounted, onUnmounted } from 'vue'
import CanvasComponent from '../components/CanvasComponent.vue'
import type { ComponentItem } from '../types/canvas'

// 响应式数据
const selectedLineType = ref('none')
const lineColor = ref('#000000')
const showContextMenu = ref(false)
const contextMenuPosition = reactive({ x: 0, y: 0 })
const canvasRef = ref()
const fileInputRef = ref()

// 组件库
const componentLibrary: ComponentItem[] = [
  { id: '1', name: '1', type: 'rectangle', width: 80, height: 40, color: '#409EFF' },
  { id: '2', name: '2', type: 'circle', width: 60, height: 60, color: '#67C23A' },
  { id: '3', name: '3', type: 'rectangle', width: 100, height: 30, color: '#E6A23C' },
  { id: '4', name: '4', type: 'circle', width: 50, height: 50, color: '#F56C6C' },
  { id: '5', name: '5', type: 'rectangle', width: 120, height: 50, color: '#909399' },
]

// 方法
const selectLineType = (type: string) => {
  selectedLineType.value = type
}

const getModeText = () => {
  switch (selectedLineType.value) {
    case 'none': return '选择模式'
    case 'arrow': return '单箭头绘制'
    case 'double-arrow': return '双箭头绘制'
    case 'line': return '直线绘制'
    case 'curve': return '曲线绘制'
    default: return '未知模式'
  }
}

const getLineTypeHint = () => {
  return '点击画布开始绘制，再次点击完成绘制'
}

const onDragStart = (event: DragEvent, component: ComponentItem) => {
  if (event.dataTransfer) {
    event.dataTransfer.setData('application/json', JSON.stringify(component))
  }
}

const getComponentStyle = (component: ComponentItem) => {
  const baseStyle = {
    backgroundColor: component.color,
    width: component.width + 'px',
    height: component.height + 'px',
    borderRadius: component.type === 'circle' ? '50%' : '4px',
    display: 'flex',
    alignItems: 'center',
    justifyContent: 'center',
    color: 'white',
    fontSize: '12px',
    cursor: 'grab'
  }
  return baseStyle
}

const onComponentSelected = (component: any) => {
  // 处理组件选择
}

const saveCanvas = () => {
  if (canvasRef.value) {
    const canvasData = canvasRef.value.getCanvasData()
    const dataStr = JSON.stringify(canvasData, null, 2)
    const dataBlob = new Blob([dataStr], { type: 'application/json' })
    
    const link = document.createElement('a')
    link.href = URL.createObjectURL(dataBlob)
    link.download = 'canvas-data.json'
    link.click()
    
    URL.revokeObjectURL(link.href)
  }
}

const loadCanvas = () => {
  fileInputRef.value?.click()
}

const onFileSelected = (event: Event) => {
  const file = (event.target as HTMLInputElement).files?.[0]
  if (file) {
    const reader = new FileReader()
    reader.onload = (e) => {
      try {
        const data = JSON.parse(e.target?.result as string)
        if (canvasRef.value) {
          canvasRef.value.loadCanvasData(data)
        }
      } catch (error) {
        console.error('Failed to load canvas data:', error)
      }
    }
    reader.readAsText(file)
  }
}

const copySelected = () => {
  if (canvasRef.value) {
    canvasRef.value.copySelected()
  }
  hideContextMenu()
}

const deleteSelected = () => {
  if (canvasRef.value) {
    canvasRef.value.deleteSelected()
  }
  hideContextMenu()
}

const alignLeft = () => {
  if (canvasRef.value) {
    canvasRef.value.alignSelected('left')
  }
  hideContextMenu()
}

const alignCenter = () => {
  if (canvasRef.value) {
    canvasRef.value.alignSelected('center')
  }
  hideContextMenu()
}

const alignRight = () => {
  if (canvasRef.value) {
    canvasRef.value.alignSelected('right')
  }
  hideContextMenu()
}

const hideContextMenu = () => {
  showContextMenu.value = false
}

const handleContextMenu = (event: MouseEvent) => {
  event.preventDefault()
  contextMenuPosition.x = event.clientX
  contextMenuPosition.y = event.clientY
  showContextMenu.value = true
}

const handleClick = () => {
  hideContextMenu()
}

const handleKeyDown = (event: KeyboardEvent) => {
  // Ctrl/Cmd + S: 保存
  if ((event.ctrlKey || event.metaKey) && event.key === 's') {
    event.preventDefault()
    saveCanvas()
  }

  // Ctrl/Cmd + C: 复制
  if ((event.ctrlKey || event.metaKey) && event.key === 'c') {
    event.preventDefault()
    copySelected()
  }

  // Delete: 删除选中
  if (event.key === 'Delete' || event.key === 'Backspace') {
    event.preventDefault()
    deleteSelected()
  }

  // Escape: 取消当前操作
  if (event.key === 'Escape') {
    selectedLineType.value = 'none'
    hideContextMenu()
  }
}

onMounted(() => {
  document.addEventListener('contextmenu', handleContextMenu)
  document.addEventListener('click', handleClick)
  document.addEventListener('keydown', handleKeyDown)
})

onUnmounted(() => {
  document.removeEventListener('contextmenu', handleContextMenu)
  document.removeEventListener('click', handleClick)
  document.removeEventListener('keydown', handleKeyDown)
})
</script>

<style scoped>
.canvas-container {
  height: 100vh;
  display: flex;
  flex-direction: column;
  background-color: #f5f5f5;
}

.top-toolbar {
  height: 60px;
  background: white;
  border-bottom: 1px solid #e4e7ed;
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 0 20px;
  box-shadow: 0 2px 4px rgba(0,0,0,0.1);
}

.line-tools {
  display: flex;
  align-items: center;
}

.save-tools {
  display: flex;
  gap: 10px;
}

.main-content {
  flex: 1;
  display: flex;
  overflow: hidden;
}

.left-panel {
  width: 200px;
  background: white;
  border-right: 1px solid #e4e7ed;
  display: flex;
  flex-direction: column;
}

.panel-title {
  padding: 15px;
  font-weight: bold;
  border-bottom: 1px solid #e4e7ed;
  background: #fafafa;
}

.component-list {
  flex: 1;
  padding: 15px;
  overflow-y: auto;
}

.component-item {
  margin-bottom: 15px;
  cursor: grab;
}

.component-item:active {
  cursor: grabbing;
}

.component-preview {
  border: 1px solid #ddd;
  transition: all 0.3s;
}

.component-preview:hover {
  transform: scale(1.05);
  box-shadow: 0 4px 8px rgba(0,0,0,0.2);
}

.canvas-area {
  flex: 1;
  position: relative;
  overflow: hidden;
}

.status-bar {
  height: 30px;
  background: #f5f5f5;
  border-top: 1px solid #e4e7ed;
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 0 15px;
  font-size: 12px;
  color: #666;
}

.status-left, .status-right {
  display: flex;
  align-items: center;
  gap: 15px;
}

.status-item {
  white-space: nowrap;
}
</style>
