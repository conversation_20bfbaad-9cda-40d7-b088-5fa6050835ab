{"version": 3, "file": "dialog-content.mjs", "sources": ["../../../../../../packages/components/dialog/src/dialog-content.ts"], "sourcesContent": ["import { buildProps, iconPropType } from '@element-plus/utils'\n\nexport const dialogContentProps = buildProps({\n  /**\n   * @description whether to align the header and footer in center\n   */\n  center: Boolean,\n  /**\n   * @description whether to align the dialog both horizontally and vertically\n   */\n  alignCenter: Boolean,\n  /**\n   * @description custom close icon, default is Close\n   */\n  closeIcon: {\n    type: iconPropType,\n  },\n  /**\n   * @description enable dragging feature for Dialog\n   */\n  draggable: Boolean,\n  /**\n   * @description draggable Dialog can overflow the viewport\n   */\n  overflow: Boolean,\n  /**\n   * @description whether the Dialog takes up full screen\n   */\n  fullscreen: Boolean,\n  /**\n   * @description custom class names for header wrapper\n   */\n  headerClass: String,\n  /**\n   * @description custom class names for body wrapper\n   */\n  bodyClass: String,\n  /**\n   * @description custom class names for footer wrapper\n   */\n  footerClass: String,\n  /**\n   * @description whether to show a close button\n   */\n  showClose: {\n    type: Boolean,\n    default: true,\n  },\n  /**\n   * @description title of Dialog. Can also be passed with a named slot (see the following table)\n   */\n  title: {\n    type: String,\n    default: '',\n  },\n  /**\n   * @description header's aria-level attribute\n   */\n  ariaLevel: {\n    type: String,\n    default: '2',\n  },\n} as const)\n\nexport const dialogContentEmits = {\n  close: () => true,\n}\n"], "names": [], "mappings": ";;;AACY,MAAC,kBAAkB,GAAG,UAAU,CAAC;AAC7C,EAAE,MAAM,EAAE,OAAO;AACjB,EAAE,WAAW,EAAE,OAAO;AACtB,EAAE,SAAS,EAAE;AACb,IAAI,IAAI,EAAE,YAAY;AACtB,GAAG;AACH,EAAE,SAAS,EAAE,OAAO;AACpB,EAAE,QAAQ,EAAE,OAAO;AACnB,EAAE,UAAU,EAAE,OAAO;AACrB,EAAE,WAAW,EAAE,MAAM;AACrB,EAAE,SAAS,EAAE,MAAM;AACnB,EAAE,WAAW,EAAE,MAAM;AACrB,EAAE,SAAS,EAAE;AACb,IAAI,IAAI,EAAE,OAAO;AACjB,IAAI,OAAO,EAAE,IAAI;AACjB,GAAG;AACH,EAAE,KAAK,EAAE;AACT,IAAI,IAAI,EAAE,MAAM;AAChB,IAAI,OAAO,EAAE,EAAE;AACf,GAAG;AACH,EAAE,SAAS,EAAE;AACb,IAAI,IAAI,EAAE,MAAM;AAChB,IAAI,OAAO,EAAE,GAAG;AAChB,GAAG;AACH,CAAC,EAAE;AACS,MAAC,kBAAkB,GAAG;AAClC,EAAE,KAAK,EAAE,MAAM,IAAI;AACnB;;;;"}