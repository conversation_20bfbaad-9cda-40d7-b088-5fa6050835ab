{"version": 3, "file": "content2.mjs", "sources": ["../../../../../../packages/components/tour/src/content.vue"], "sourcesContent": ["<template>\n  <div\n    ref=\"contentRef\"\n    :style=\"contentStyle\"\n    :class=\"ns.e('content')\"\n    :data-side=\"side\"\n    tabindex=\"-1\"\n  >\n    <el-focus-trap\n      loop\n      trapped\n      focus-start-el=\"container\"\n      :focus-trap-el=\"contentRef || undefined\"\n      @release-requested=\"onCloseRequested\"\n      @focusout-prevented=\"onFocusoutPrevented\"\n    >\n      <slot />\n    </el-focus-trap>\n    <span\n      v-if=\"showArrow\"\n      ref=\"arrowRef\"\n      :style=\"arrowStyle\"\n      :class=\"ns.e('arrow')\"\n    />\n  </div>\n</template>\n\n<script setup lang=\"ts\">\nimport { computed, inject, ref, toRef, watch } from 'vue'\nimport ElFocusTrap from '@element-plus/components/focus-trap'\nimport { tourContentEmits, tourContentProps } from './content'\nimport { tourKey, useFloating } from './helper'\n\ndefineOptions({\n  name: 'ElTourContent',\n})\n\nconst props = defineProps(tourContentProps)\nconst emit = defineEmits(tourContentEmits)\n\nconst placement = ref(props.placement)\nconst strategy = ref(props.strategy)\nconst contentRef = ref<HTMLElement | null>(null)\nconst arrowRef = ref<HTMLElement | null>(null)\n\nwatch(\n  () => props.placement,\n  () => {\n    placement.value = props.placement\n  }\n)\n\nconst { contentStyle, arrowStyle } = useFloating(\n  toRef(props, 'reference'),\n  contentRef,\n  arrowRef,\n  placement,\n  strategy,\n  toRef(props, 'offset'),\n  toRef(props, 'zIndex'),\n  toRef(props, 'showArrow')\n)\n\nconst side = computed(() => {\n  return placement.value.split('-')[0]\n})\n\nconst { ns } = inject(tourKey)!\n\nconst onCloseRequested = () => {\n  emit('close')\n}\n\nconst onFocusoutPrevented = (event: CustomEvent) => {\n  if (event.detail.focusReason === 'pointer') {\n    event.preventDefault()\n  }\n}\n</script>\n"], "names": ["_openBlock", "_createElementBlock", "_unref", "_normalizeClass", "_createVNode"], "mappings": ";;;;;;mCAiCc,CAAA;AAAA,EACZ,IAAM,EAAA,eAAA;AACR,CAAA,CAAA,CAAA;;;;;;;AAKA,IAAM,MAAA,SAAA,GAAY,GAAI,CAAA,KAAA,CAAM,SAAS,CAAA,CAAA;AACrC,IAAM,MAAA,QAAA,GAAW,GAAI,CAAA,KAAA,CAAM,QAAQ,CAAA,CAAA;AACnC,IAAM,MAAA,UAAA,GAAa,IAAwB,IAAI,CAAA,CAAA;AAC/C,IAAM,MAAA,QAAA,GAAW,IAAwB,IAAI,CAAA,CAAA;AAE7C,IAAA,KAAA,CAAA,MAAA,KAAA,CAAA,SAAA,EAAA,MAAA;AAAA,MACE,SAAY,CAAA,KAAA,GAAA,KAAA,CAAA,SAAA,CAAA;AAAA,KAAA,CACZ,CAAM;AACJ,IAAA,MAAA,EAAA,cAAkB,UAAM,EAAA,GAAA,WAAA,CAAA,KAAA,CAAA,KAAA,EAAA,WAAA,CAAA,EAAA,UAAA,EAAA,QAAA,EAAA,SAAA,EAAA,QAAA,EAAA,KAAA,CAAA,KAAA,EAAA,QAAA,CAAA,EAAA,KAAA,CAAA,KAAA,EAAA,QAAA,CAAA,EAAA,KAAA,CAAA,KAAA,EAAA,WAAA,CAAA,CAAA,CAAA;AAAA,IAC1B,MAAA,IAAA,GAAA,QAAA,CAAA,MAAA;AAAA,MACF,OAAA,SAAA,CAAA,KAAA,CAAA,KAAA,CAAA,GAAA,CAAA,CAAA,CAAA,CAAA,CAAA;AAEA,KAAM,CAAA,CAAA;AAA+B,IACnC,MAAA,EAAM,OAAO,MAAW,CAAA,OAAA,CAAA,CAAA;AAAA,IACxB,MAAA,gBAAA,GAAA,MAAA;AAAA,MACA,IAAA,CAAA,OAAA,CAAA,CAAA;AAAA,KACA,CAAA;AAAA,IACA,MAAA,mBAAA,GAAA,CAAA,KAAA,KAAA;AAAA,MACA,IAAA,YAAqB,CAAA,WAAA,KAAA,SAAA,EAAA;AAAA,QACrB,oBAAqB,EAAA,CAAA;AAAA,OACrB;AAAwB,KAC1B,CAAA;AAEA,IAAM,OAAA,CAAA,IAAA,aAAsB;AAC1B,MAAA,OAAOA,SAAU,EAAA,EAAAC,kBAAkB,CAAA,KAAA,EAAA;AAAA,QACpC,OAAA,EAAA,YAAA;AAED,QAAA,GAAQ,EAAA,UAAO;AAEf,QAAA,qBAA+B,CAAAC,KAAA,CAAA,YAAA,CAAA,CAAA;AAC7B,QAAA,KAAY,EAAAC,cAAA,CAAAD,KAAA,CAAA,EAAA,CAAA,CAAA,CAAA,CAAA,SAAA,CAAA,CAAA;AAAA,QACd,WAAA,EAAAA,KAAA,CAAA,IAAA,CAAA;AAEA,QAAM,QAAA,EAAA,IAAA;AACJ,OAAI,EAAA;AACF,QAAAE,WAAqB,CAAAF,KAAA,CAAA,WAAA,CAAA,EAAA;AAAA,UACvB,IAAA,EAAA,EAAA;AAAA,UACF,OAAA,EAAA,EAAA;;;;;;;;;;;;;;;;;;;;;;;;;;"}