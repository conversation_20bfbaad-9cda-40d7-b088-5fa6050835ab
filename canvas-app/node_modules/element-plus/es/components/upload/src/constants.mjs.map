{"version": 3, "file": "constants.mjs", "sources": ["../../../../../../packages/components/upload/src/constants.ts"], "sourcesContent": ["import type { ComputedRef, InjectionKey } from 'vue'\n\nexport interface UploadContext {\n  accept: ComputedRef<string>\n}\n\nexport const uploadContextKey: InjectionKey<UploadContext> =\n  Symbol('uploadContextKey')\n"], "names": [], "mappings": "AAAY,MAAC,gBAAgB,GAAG,MAAM,CAAC,kBAAkB;;;;"}