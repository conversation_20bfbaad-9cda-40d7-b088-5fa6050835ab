{"version": 3, "file": "checkbox-group.js", "sources": ["../../../../../../packages/components/checkbox/src/checkbox-group.ts"], "sourcesContent": ["import { UPDATE_MODEL_EVENT } from '@element-plus/constants'\nimport { useAriaProps, useSizeProp } from '@element-plus/hooks'\nimport { buildProps, definePropType, isArray } from '@element-plus/utils'\n\nimport type { ExtractPropTypes } from 'vue'\nimport type checkboxGroup from './checkbox-group.vue'\nimport type { CheckboxValueType } from './checkbox'\n\nexport type CheckboxGroupValueType = Exclude<CheckboxValueType, boolean>[]\n\nexport const checkboxGroupProps = buildProps({\n  /**\n   * @description binding value\n   */\n  modelValue: {\n    type: definePropType<CheckboxGroupValueType>(Array),\n    default: () => [],\n  },\n  /**\n   * @description whether the nesting checkboxes are disabled\n   */\n  disabled: Boolean,\n  /**\n   * @description minimum number of checkbox checked\n   */\n  min: Number,\n  /**\n   * @description maximum number of checkbox checked\n   */\n  max: Number,\n  /**\n   * @description size of checkbox\n   */\n  size: useSizeProp,\n  /**\n   * @description border and background color when button is active\n   */\n  fill: String,\n  /**\n   * @description font color when button is active\n   */\n  textColor: String,\n  /**\n   * @description element tag of the checkbox group\n   */\n  tag: {\n    type: String,\n    default: 'div',\n  },\n  /**\n   * @description whether to trigger form validation\n   */\n  validateEvent: {\n    type: Boolean,\n    default: true,\n  },\n  ...useAriaProps(['ariaLabel']),\n} as const)\n\nexport const checkboxGroupEmits = {\n  [UPDATE_MODEL_EVENT]: (val: CheckboxGroupValueType) => isArray(val),\n  change: (val: CheckboxValueType[]) => isArray(val),\n}\n\nexport type CheckboxGroupProps = ExtractPropTypes<typeof checkboxGroupProps>\nexport type CheckboxGroupEmits = typeof checkboxGroupEmits\nexport type CheckboxGroupInstance = InstanceType<typeof checkboxGroup> & unknown\n"], "names": ["buildProps", "definePropType", "useSizeProp", "useAriaProps", "UPDATE_MODEL_EVENT", "isArray"], "mappings": ";;;;;;;;;;AAGY,MAAC,kBAAkB,GAAGA,kBAAU,CAAC;AAC7C,EAAE,UAAU,EAAE;AACd,IAAI,IAAI,EAAEC,sBAAc,CAAC,KAAK,CAAC;AAC/B,IAAI,OAAO,EAAE,MAAM,EAAE;AACrB,GAAG;AACH,EAAE,QAAQ,EAAE,OAAO;AACnB,EAAE,GAAG,EAAE,MAAM;AACb,EAAE,GAAG,EAAE,MAAM;AACb,EAAE,IAAI,EAAEC,iBAAW;AACnB,EAAE,IAAI,EAAE,MAAM;AACd,EAAE,SAAS,EAAE,MAAM;AACnB,EAAE,GAAG,EAAE;AACP,IAAI,IAAI,EAAE,MAAM;AAChB,IAAI,OAAO,EAAE,KAAK;AAClB,GAAG;AACH,EAAE,aAAa,EAAE;AACjB,IAAI,IAAI,EAAE,OAAO;AACjB,IAAI,OAAO,EAAE,IAAI;AACjB,GAAG;AACH,EAAE,GAAGC,oBAAY,CAAC,CAAC,WAAW,CAAC,CAAC;AAChC,CAAC,EAAE;AACS,MAAC,kBAAkB,GAAG;AAClC,EAAE,CAACC,wBAAkB,GAAG,CAAC,GAAG,KAAKC,cAAO,CAAC,GAAG,CAAC;AAC7C,EAAE,MAAM,EAAE,CAAC,GAAG,KAAKA,cAAO,CAAC,GAAG,CAAC;AAC/B;;;;;"}