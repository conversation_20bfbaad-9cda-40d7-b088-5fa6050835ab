export type MessageIds = 'comparingNullableToFalse' | 'comparingNullableToTrueDirect' | 'comparingNullableToTrueNegated' | 'direct' | 'negated' | 'noStrictNullCheck';
export type Options = [
    {
        allowComparingNullableBooleansToFalse?: boolean;
        allowComparingNullableBooleansToTrue?: boolean;
        allowRuleToRunWithoutStrictNullChecksIKnowWhatIAmDoing?: boolean;
    }
];
declare const _default: import("@typescript-eslint/utils/ts-eslint").RuleModule<MessageIds, Options, import("../../rules").ESLintPluginDocs, import("@typescript-eslint/utils/ts-eslint").RuleListener>;
export default _default;
//# sourceMappingURL=no-unnecessary-boolean-literal-compare.d.ts.map