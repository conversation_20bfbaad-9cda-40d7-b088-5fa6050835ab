import type { TSESLint } from '@typescript-eslint/utils';
import type { FlatConfig } from '@typescript-eslint/utils/ts-eslint';
declare const _default: {
    flatConfigs: {
        'flat/all': FlatConfig.ConfigArray;
        'flat/base': FlatConfig.Config;
        'flat/disable-type-checked': FlatConfig.Config;
        'flat/eslint-recommended': FlatConfig.Config;
        'flat/recommended': FlatConfig.ConfigArray;
        'flat/recommended-type-checked': FlatConfig.ConfigArray;
        'flat/recommended-type-checked-only': FlatConfig.ConfigArray;
        'flat/strict': FlatConfig.ConfigArray;
        'flat/strict-type-checked': FlatConfig.ConfigArray;
        'flat/strict-type-checked-only': FlatConfig.ConfigArray;
        'flat/stylistic': FlatConfig.ConfigArray;
        'flat/stylistic-type-checked': FlatConfig.ConfigArray;
        'flat/stylistic-type-checked-only': FlatConfig.ConfigArray;
    };
    parser: {
        meta?: { [K in keyof TSESLint.Parser.ParserMeta]?: TSESLint.Parser.ParserMeta[K] | undefined; };
        parseForESLint(text: string, options?: unknown): { [k in keyof TSESLint.Parser.ParseResult]: unknown; };
    };
    plugin: {
        configs: {
            all: {
                extends: string[];
                rules: {
                    '@typescript-eslint/adjacent-overload-signatures': "error";
                    '@typescript-eslint/array-type': "error";
                    '@typescript-eslint/await-thenable': "error";
                    '@typescript-eslint/ban-ts-comment': "error";
                    '@typescript-eslint/ban-tslint-comment': "error";
                    '@typescript-eslint/class-literal-property-style': "error";
                    'class-methods-use-this': "off";
                    '@typescript-eslint/class-methods-use-this': "error";
                    '@typescript-eslint/consistent-generic-constructors': "error";
                    '@typescript-eslint/consistent-indexed-object-style': "error";
                    'consistent-return': "off";
                    '@typescript-eslint/consistent-return': "error";
                    '@typescript-eslint/consistent-type-assertions': "error";
                    '@typescript-eslint/consistent-type-definitions': "error";
                    '@typescript-eslint/consistent-type-exports': "error";
                    '@typescript-eslint/consistent-type-imports': "error";
                    'default-param-last': "off";
                    '@typescript-eslint/default-param-last': "error";
                    'dot-notation': "off";
                    '@typescript-eslint/dot-notation': "error";
                    '@typescript-eslint/explicit-function-return-type': "error";
                    '@typescript-eslint/explicit-member-accessibility': "error";
                    '@typescript-eslint/explicit-module-boundary-types': "error";
                    'init-declarations': "off";
                    '@typescript-eslint/init-declarations': "error";
                    'max-params': "off";
                    '@typescript-eslint/max-params': "error";
                    '@typescript-eslint/member-ordering': "error";
                    '@typescript-eslint/method-signature-style': "error";
                    '@typescript-eslint/naming-convention': "error";
                    'no-array-constructor': "off";
                    '@typescript-eslint/no-array-constructor': "error";
                    '@typescript-eslint/no-array-delete': "error";
                    '@typescript-eslint/no-base-to-string': "error";
                    '@typescript-eslint/no-confusing-non-null-assertion': "error";
                    '@typescript-eslint/no-confusing-void-expression': "error";
                    '@typescript-eslint/no-deprecated': "error";
                    'no-dupe-class-members': "off";
                    '@typescript-eslint/no-dupe-class-members': "error";
                    '@typescript-eslint/no-duplicate-enum-values': "error";
                    '@typescript-eslint/no-duplicate-type-constituents': "error";
                    '@typescript-eslint/no-dynamic-delete': "error";
                    'no-empty-function': "off";
                    '@typescript-eslint/no-empty-function': "error";
                    '@typescript-eslint/no-empty-object-type': "error";
                    '@typescript-eslint/no-explicit-any': "error";
                    '@typescript-eslint/no-extra-non-null-assertion': "error";
                    '@typescript-eslint/no-extraneous-class': "error";
                    '@typescript-eslint/no-floating-promises': "error";
                    '@typescript-eslint/no-for-in-array': "error";
                    'no-implied-eval': "off";
                    '@typescript-eslint/no-implied-eval': "error";
                    '@typescript-eslint/no-import-type-side-effects': "error";
                    '@typescript-eslint/no-inferrable-types': "error";
                    'no-invalid-this': "off";
                    '@typescript-eslint/no-invalid-this': "error";
                    '@typescript-eslint/no-invalid-void-type': "error";
                    'no-loop-func': "off";
                    '@typescript-eslint/no-loop-func': "error";
                    'no-magic-numbers': "off";
                    '@typescript-eslint/no-magic-numbers': "error";
                    '@typescript-eslint/no-meaningless-void-operator': "error";
                    '@typescript-eslint/no-misused-new': "error";
                    '@typescript-eslint/no-misused-promises': "error";
                    '@typescript-eslint/no-misused-spread': "error";
                    '@typescript-eslint/no-mixed-enums': "error";
                    '@typescript-eslint/no-namespace': "error";
                    '@typescript-eslint/no-non-null-asserted-nullish-coalescing': "error";
                    '@typescript-eslint/no-non-null-asserted-optional-chain': "error";
                    '@typescript-eslint/no-non-null-assertion': "error";
                    'no-redeclare': "off";
                    '@typescript-eslint/no-redeclare': "error";
                    '@typescript-eslint/no-redundant-type-constituents': "error";
                    '@typescript-eslint/no-require-imports': "error";
                    'no-restricted-imports': "off";
                    '@typescript-eslint/no-restricted-imports': "error";
                    '@typescript-eslint/no-restricted-types': "error";
                    'no-shadow': "off";
                    '@typescript-eslint/no-shadow': "error";
                    '@typescript-eslint/no-this-alias': "error";
                    '@typescript-eslint/no-unnecessary-boolean-literal-compare': "error";
                    '@typescript-eslint/no-unnecessary-condition': "error";
                    '@typescript-eslint/no-unnecessary-parameter-property-assignment': "error";
                    '@typescript-eslint/no-unnecessary-qualifier': "error";
                    '@typescript-eslint/no-unnecessary-template-expression': "error";
                    '@typescript-eslint/no-unnecessary-type-arguments': "error";
                    '@typescript-eslint/no-unnecessary-type-assertion': "error";
                    '@typescript-eslint/no-unnecessary-type-constraint': "error";
                    '@typescript-eslint/no-unnecessary-type-conversion': "error";
                    '@typescript-eslint/no-unnecessary-type-parameters': "error";
                    '@typescript-eslint/no-unsafe-argument': "error";
                    '@typescript-eslint/no-unsafe-assignment': "error";
                    '@typescript-eslint/no-unsafe-call': "error";
                    '@typescript-eslint/no-unsafe-declaration-merging': "error";
                    '@typescript-eslint/no-unsafe-enum-comparison': "error";
                    '@typescript-eslint/no-unsafe-function-type': "error";
                    '@typescript-eslint/no-unsafe-member-access': "error";
                    '@typescript-eslint/no-unsafe-return': "error";
                    '@typescript-eslint/no-unsafe-type-assertion': "error";
                    '@typescript-eslint/no-unsafe-unary-minus': "error";
                    'no-unused-expressions': "off";
                    '@typescript-eslint/no-unused-expressions': "error";
                    'no-unused-vars': "off";
                    '@typescript-eslint/no-unused-vars': "error";
                    'no-use-before-define': "off";
                    '@typescript-eslint/no-use-before-define': "error";
                    'no-useless-constructor': "off";
                    '@typescript-eslint/no-useless-constructor': "error";
                    '@typescript-eslint/no-useless-empty-export': "error";
                    '@typescript-eslint/no-wrapper-object-types': "error";
                    '@typescript-eslint/non-nullable-type-assertion-style': "error";
                    'no-throw-literal': "off";
                    '@typescript-eslint/only-throw-error': "error";
                    '@typescript-eslint/parameter-properties': "error";
                    '@typescript-eslint/prefer-as-const': "error";
                    'prefer-destructuring': "off";
                    '@typescript-eslint/prefer-destructuring': "error";
                    '@typescript-eslint/prefer-enum-initializers': "error";
                    '@typescript-eslint/prefer-find': "error";
                    '@typescript-eslint/prefer-for-of': "error";
                    '@typescript-eslint/prefer-function-type': "error";
                    '@typescript-eslint/prefer-includes': "error";
                    '@typescript-eslint/prefer-literal-enum-member': "error";
                    '@typescript-eslint/prefer-namespace-keyword': "error";
                    '@typescript-eslint/prefer-nullish-coalescing': "error";
                    '@typescript-eslint/prefer-optional-chain': "error";
                    'prefer-promise-reject-errors': "off";
                    '@typescript-eslint/prefer-promise-reject-errors': "error";
                    '@typescript-eslint/prefer-readonly': "error";
                    '@typescript-eslint/prefer-readonly-parameter-types': "error";
                    '@typescript-eslint/prefer-reduce-type-parameter': "error";
                    '@typescript-eslint/prefer-regexp-exec': "error";
                    '@typescript-eslint/prefer-return-this-type': "error";
                    '@typescript-eslint/prefer-string-starts-ends-with': "error";
                    '@typescript-eslint/promise-function-async': "error";
                    '@typescript-eslint/related-getter-setter-pairs': "error";
                    '@typescript-eslint/require-array-sort-compare': "error";
                    'require-await': "off";
                    '@typescript-eslint/require-await': "error";
                    '@typescript-eslint/restrict-plus-operands': "error";
                    '@typescript-eslint/restrict-template-expressions': "error";
                    'no-return-await': "off";
                    '@typescript-eslint/return-await': "error";
                    '@typescript-eslint/strict-boolean-expressions': "error";
                    '@typescript-eslint/switch-exhaustiveness-check': "error";
                    '@typescript-eslint/triple-slash-reference': "error";
                    '@typescript-eslint/unbound-method': "error";
                    '@typescript-eslint/unified-signatures': "error";
                    '@typescript-eslint/use-unknown-in-catch-callback-variable': "error";
                };
            };
            base: {
                parser: string;
                parserOptions: {
                    sourceType: "module";
                };
                plugins: string[];
            };
            'disable-type-checked': {
                parserOptions: {
                    program: null;
                    project: false;
                    projectService: false;
                };
                rules: {
                    '@typescript-eslint/await-thenable': "off";
                    '@typescript-eslint/consistent-return': "off";
                    '@typescript-eslint/consistent-type-exports': "off";
                    '@typescript-eslint/dot-notation': "off";
                    '@typescript-eslint/naming-convention': "off";
                    '@typescript-eslint/no-array-delete': "off";
                    '@typescript-eslint/no-base-to-string': "off";
                    '@typescript-eslint/no-confusing-void-expression': "off";
                    '@typescript-eslint/no-deprecated': "off";
                    '@typescript-eslint/no-duplicate-type-constituents': "off";
                    '@typescript-eslint/no-floating-promises': "off";
                    '@typescript-eslint/no-for-in-array': "off";
                    '@typescript-eslint/no-implied-eval': "off";
                    '@typescript-eslint/no-meaningless-void-operator': "off";
                    '@typescript-eslint/no-misused-promises': "off";
                    '@typescript-eslint/no-misused-spread': "off";
                    '@typescript-eslint/no-mixed-enums': "off";
                    '@typescript-eslint/no-redundant-type-constituents': "off";
                    '@typescript-eslint/no-unnecessary-boolean-literal-compare': "off";
                    '@typescript-eslint/no-unnecessary-condition': "off";
                    '@typescript-eslint/no-unnecessary-qualifier': "off";
                    '@typescript-eslint/no-unnecessary-template-expression': "off";
                    '@typescript-eslint/no-unnecessary-type-arguments': "off";
                    '@typescript-eslint/no-unnecessary-type-assertion': "off";
                    '@typescript-eslint/no-unnecessary-type-conversion': "off";
                    '@typescript-eslint/no-unnecessary-type-parameters': "off";
                    '@typescript-eslint/no-unsafe-argument': "off";
                    '@typescript-eslint/no-unsafe-assignment': "off";
                    '@typescript-eslint/no-unsafe-call': "off";
                    '@typescript-eslint/no-unsafe-enum-comparison': "off";
                    '@typescript-eslint/no-unsafe-member-access': "off";
                    '@typescript-eslint/no-unsafe-return': "off";
                    '@typescript-eslint/no-unsafe-type-assertion': "off";
                    '@typescript-eslint/no-unsafe-unary-minus': "off";
                    '@typescript-eslint/non-nullable-type-assertion-style': "off";
                    '@typescript-eslint/only-throw-error': "off";
                    '@typescript-eslint/prefer-destructuring': "off";
                    '@typescript-eslint/prefer-find': "off";
                    '@typescript-eslint/prefer-includes': "off";
                    '@typescript-eslint/prefer-nullish-coalescing': "off";
                    '@typescript-eslint/prefer-optional-chain': "off";
                    '@typescript-eslint/prefer-promise-reject-errors': "off";
                    '@typescript-eslint/prefer-readonly': "off";
                    '@typescript-eslint/prefer-readonly-parameter-types': "off";
                    '@typescript-eslint/prefer-reduce-type-parameter': "off";
                    '@typescript-eslint/prefer-regexp-exec': "off";
                    '@typescript-eslint/prefer-return-this-type': "off";
                    '@typescript-eslint/prefer-string-starts-ends-with': "off";
                    '@typescript-eslint/promise-function-async': "off";
                    '@typescript-eslint/related-getter-setter-pairs': "off";
                    '@typescript-eslint/require-array-sort-compare': "off";
                    '@typescript-eslint/require-await': "off";
                    '@typescript-eslint/restrict-plus-operands': "off";
                    '@typescript-eslint/restrict-template-expressions': "off";
                    '@typescript-eslint/return-await': "off";
                    '@typescript-eslint/strict-boolean-expressions': "off";
                    '@typescript-eslint/switch-exhaustiveness-check': "off";
                    '@typescript-eslint/unbound-method': "off";
                    '@typescript-eslint/use-unknown-in-catch-callback-variable': "off";
                };
            };
            'eslint-recommended': {
                overrides: {
                    files: string[];
                    rules: Record<string, "error" | "off" | "warn">;
                }[];
            };
            recommended: {
                extends: string[];
                rules: {
                    '@typescript-eslint/ban-ts-comment': "error";
                    'no-array-constructor': "off";
                    '@typescript-eslint/no-array-constructor': "error";
                    '@typescript-eslint/no-duplicate-enum-values': "error";
                    '@typescript-eslint/no-empty-object-type': "error";
                    '@typescript-eslint/no-explicit-any': "error";
                    '@typescript-eslint/no-extra-non-null-assertion': "error";
                    '@typescript-eslint/no-misused-new': "error";
                    '@typescript-eslint/no-namespace': "error";
                    '@typescript-eslint/no-non-null-asserted-optional-chain': "error";
                    '@typescript-eslint/no-require-imports': "error";
                    '@typescript-eslint/no-this-alias': "error";
                    '@typescript-eslint/no-unnecessary-type-constraint': "error";
                    '@typescript-eslint/no-unsafe-declaration-merging': "error";
                    '@typescript-eslint/no-unsafe-function-type': "error";
                    'no-unused-expressions': "off";
                    '@typescript-eslint/no-unused-expressions': "error";
                    'no-unused-vars': "off";
                    '@typescript-eslint/no-unused-vars': "error";
                    '@typescript-eslint/no-wrapper-object-types': "error";
                    '@typescript-eslint/prefer-as-const': "error";
                    '@typescript-eslint/prefer-namespace-keyword': "error";
                    '@typescript-eslint/triple-slash-reference': "error";
                };
            };
            /** @deprecated - please use "recommended-type-checked" instead. */
            'recommended-requiring-type-checking': {
                extends: string[];
                rules: {
                    '@typescript-eslint/await-thenable': "error";
                    '@typescript-eslint/ban-ts-comment': "error";
                    'no-array-constructor': "off";
                    '@typescript-eslint/no-array-constructor': "error";
                    '@typescript-eslint/no-array-delete': "error";
                    '@typescript-eslint/no-base-to-string': "error";
                    '@typescript-eslint/no-duplicate-enum-values': "error";
                    '@typescript-eslint/no-duplicate-type-constituents': "error";
                    '@typescript-eslint/no-empty-object-type': "error";
                    '@typescript-eslint/no-explicit-any': "error";
                    '@typescript-eslint/no-extra-non-null-assertion': "error";
                    '@typescript-eslint/no-floating-promises': "error";
                    '@typescript-eslint/no-for-in-array': "error";
                    'no-implied-eval': "off";
                    '@typescript-eslint/no-implied-eval': "error";
                    '@typescript-eslint/no-misused-new': "error";
                    '@typescript-eslint/no-misused-promises': "error";
                    '@typescript-eslint/no-namespace': "error";
                    '@typescript-eslint/no-non-null-asserted-optional-chain': "error";
                    '@typescript-eslint/no-redundant-type-constituents': "error";
                    '@typescript-eslint/no-require-imports': "error";
                    '@typescript-eslint/no-this-alias': "error";
                    '@typescript-eslint/no-unnecessary-type-assertion': "error";
                    '@typescript-eslint/no-unnecessary-type-constraint': "error";
                    '@typescript-eslint/no-unsafe-argument': "error";
                    '@typescript-eslint/no-unsafe-assignment': "error";
                    '@typescript-eslint/no-unsafe-call': "error";
                    '@typescript-eslint/no-unsafe-declaration-merging': "error";
                    '@typescript-eslint/no-unsafe-enum-comparison': "error";
                    '@typescript-eslint/no-unsafe-function-type': "error";
                    '@typescript-eslint/no-unsafe-member-access': "error";
                    '@typescript-eslint/no-unsafe-return': "error";
                    '@typescript-eslint/no-unsafe-unary-minus': "error";
                    'no-unused-expressions': "off";
                    '@typescript-eslint/no-unused-expressions': "error";
                    'no-unused-vars': "off";
                    '@typescript-eslint/no-unused-vars': "error";
                    '@typescript-eslint/no-wrapper-object-types': "error";
                    'no-throw-literal': "off";
                    '@typescript-eslint/only-throw-error': "error";
                    '@typescript-eslint/prefer-as-const': "error";
                    '@typescript-eslint/prefer-namespace-keyword': "error";
                    'prefer-promise-reject-errors': "off";
                    '@typescript-eslint/prefer-promise-reject-errors': "error";
                    'require-await': "off";
                    '@typescript-eslint/require-await': "error";
                    '@typescript-eslint/restrict-plus-operands': "error";
                    '@typescript-eslint/restrict-template-expressions': "error";
                    '@typescript-eslint/triple-slash-reference': "error";
                    '@typescript-eslint/unbound-method': "error";
                };
            };
            'recommended-type-checked': {
                extends: string[];
                rules: {
                    '@typescript-eslint/await-thenable': "error";
                    '@typescript-eslint/ban-ts-comment': "error";
                    'no-array-constructor': "off";
                    '@typescript-eslint/no-array-constructor': "error";
                    '@typescript-eslint/no-array-delete': "error";
                    '@typescript-eslint/no-base-to-string': "error";
                    '@typescript-eslint/no-duplicate-enum-values': "error";
                    '@typescript-eslint/no-duplicate-type-constituents': "error";
                    '@typescript-eslint/no-empty-object-type': "error";
                    '@typescript-eslint/no-explicit-any': "error";
                    '@typescript-eslint/no-extra-non-null-assertion': "error";
                    '@typescript-eslint/no-floating-promises': "error";
                    '@typescript-eslint/no-for-in-array': "error";
                    'no-implied-eval': "off";
                    '@typescript-eslint/no-implied-eval': "error";
                    '@typescript-eslint/no-misused-new': "error";
                    '@typescript-eslint/no-misused-promises': "error";
                    '@typescript-eslint/no-namespace': "error";
                    '@typescript-eslint/no-non-null-asserted-optional-chain': "error";
                    '@typescript-eslint/no-redundant-type-constituents': "error";
                    '@typescript-eslint/no-require-imports': "error";
                    '@typescript-eslint/no-this-alias': "error";
                    '@typescript-eslint/no-unnecessary-type-assertion': "error";
                    '@typescript-eslint/no-unnecessary-type-constraint': "error";
                    '@typescript-eslint/no-unsafe-argument': "error";
                    '@typescript-eslint/no-unsafe-assignment': "error";
                    '@typescript-eslint/no-unsafe-call': "error";
                    '@typescript-eslint/no-unsafe-declaration-merging': "error";
                    '@typescript-eslint/no-unsafe-enum-comparison': "error";
                    '@typescript-eslint/no-unsafe-function-type': "error";
                    '@typescript-eslint/no-unsafe-member-access': "error";
                    '@typescript-eslint/no-unsafe-return': "error";
                    '@typescript-eslint/no-unsafe-unary-minus': "error";
                    'no-unused-expressions': "off";
                    '@typescript-eslint/no-unused-expressions': "error";
                    'no-unused-vars': "off";
                    '@typescript-eslint/no-unused-vars': "error";
                    '@typescript-eslint/no-wrapper-object-types': "error";
                    'no-throw-literal': "off";
                    '@typescript-eslint/only-throw-error': "error";
                    '@typescript-eslint/prefer-as-const': "error";
                    '@typescript-eslint/prefer-namespace-keyword': "error";
                    'prefer-promise-reject-errors': "off";
                    '@typescript-eslint/prefer-promise-reject-errors': "error";
                    'require-await': "off";
                    '@typescript-eslint/require-await': "error";
                    '@typescript-eslint/restrict-plus-operands': "error";
                    '@typescript-eslint/restrict-template-expressions': "error";
                    '@typescript-eslint/triple-slash-reference': "error";
                    '@typescript-eslint/unbound-method': "error";
                };
            };
            'recommended-type-checked-only': {
                extends: string[];
                rules: {
                    '@typescript-eslint/await-thenable': "error";
                    '@typescript-eslint/no-array-delete': "error";
                    '@typescript-eslint/no-base-to-string': "error";
                    '@typescript-eslint/no-duplicate-type-constituents': "error";
                    '@typescript-eslint/no-floating-promises': "error";
                    '@typescript-eslint/no-for-in-array': "error";
                    'no-implied-eval': "off";
                    '@typescript-eslint/no-implied-eval': "error";
                    '@typescript-eslint/no-misused-promises': "error";
                    '@typescript-eslint/no-redundant-type-constituents': "error";
                    '@typescript-eslint/no-unnecessary-type-assertion': "error";
                    '@typescript-eslint/no-unsafe-argument': "error";
                    '@typescript-eslint/no-unsafe-assignment': "error";
                    '@typescript-eslint/no-unsafe-call': "error";
                    '@typescript-eslint/no-unsafe-enum-comparison': "error";
                    '@typescript-eslint/no-unsafe-member-access': "error";
                    '@typescript-eslint/no-unsafe-return': "error";
                    '@typescript-eslint/no-unsafe-unary-minus': "error";
                    'no-throw-literal': "off";
                    '@typescript-eslint/only-throw-error': "error";
                    'prefer-promise-reject-errors': "off";
                    '@typescript-eslint/prefer-promise-reject-errors': "error";
                    'require-await': "off";
                    '@typescript-eslint/require-await': "error";
                    '@typescript-eslint/restrict-plus-operands': "error";
                    '@typescript-eslint/restrict-template-expressions': "error";
                    '@typescript-eslint/unbound-method': "error";
                };
            };
            strict: {
                extends: string[];
                rules: {
                    '@typescript-eslint/ban-ts-comment': ["error", {
                        minimumDescriptionLength: number;
                    }];
                    'no-array-constructor': "off";
                    '@typescript-eslint/no-array-constructor': "error";
                    '@typescript-eslint/no-duplicate-enum-values': "error";
                    '@typescript-eslint/no-dynamic-delete': "error";
                    '@typescript-eslint/no-empty-object-type': "error";
                    '@typescript-eslint/no-explicit-any': "error";
                    '@typescript-eslint/no-extra-non-null-assertion': "error";
                    '@typescript-eslint/no-extraneous-class': "error";
                    '@typescript-eslint/no-invalid-void-type': "error";
                    '@typescript-eslint/no-misused-new': "error";
                    '@typescript-eslint/no-namespace': "error";
                    '@typescript-eslint/no-non-null-asserted-nullish-coalescing': "error";
                    '@typescript-eslint/no-non-null-asserted-optional-chain': "error";
                    '@typescript-eslint/no-non-null-assertion': "error";
                    '@typescript-eslint/no-require-imports': "error";
                    '@typescript-eslint/no-this-alias': "error";
                    '@typescript-eslint/no-unnecessary-type-constraint': "error";
                    '@typescript-eslint/no-unsafe-declaration-merging': "error";
                    '@typescript-eslint/no-unsafe-function-type': "error";
                    'no-unused-expressions': "off";
                    '@typescript-eslint/no-unused-expressions': "error";
                    'no-unused-vars': "off";
                    '@typescript-eslint/no-unused-vars': "error";
                    'no-useless-constructor': "off";
                    '@typescript-eslint/no-useless-constructor': "error";
                    '@typescript-eslint/no-wrapper-object-types': "error";
                    '@typescript-eslint/prefer-as-const': "error";
                    '@typescript-eslint/prefer-literal-enum-member': "error";
                    '@typescript-eslint/prefer-namespace-keyword': "error";
                    '@typescript-eslint/triple-slash-reference': "error";
                    '@typescript-eslint/unified-signatures': "error";
                };
            };
            'strict-type-checked': {
                extends: string[];
                rules: {
                    '@typescript-eslint/await-thenable': "error";
                    '@typescript-eslint/ban-ts-comment': ["error", {
                        minimumDescriptionLength: number;
                    }];
                    'no-array-constructor': "off";
                    '@typescript-eslint/no-array-constructor': "error";
                    '@typescript-eslint/no-array-delete': "error";
                    '@typescript-eslint/no-base-to-string': "error";
                    '@typescript-eslint/no-confusing-void-expression': "error";
                    '@typescript-eslint/no-deprecated': "error";
                    '@typescript-eslint/no-duplicate-enum-values': "error";
                    '@typescript-eslint/no-duplicate-type-constituents': "error";
                    '@typescript-eslint/no-dynamic-delete': "error";
                    '@typescript-eslint/no-empty-object-type': "error";
                    '@typescript-eslint/no-explicit-any': "error";
                    '@typescript-eslint/no-extra-non-null-assertion': "error";
                    '@typescript-eslint/no-extraneous-class': "error";
                    '@typescript-eslint/no-floating-promises': "error";
                    '@typescript-eslint/no-for-in-array': "error";
                    'no-implied-eval': "off";
                    '@typescript-eslint/no-implied-eval': "error";
                    '@typescript-eslint/no-invalid-void-type': "error";
                    '@typescript-eslint/no-meaningless-void-operator': "error";
                    '@typescript-eslint/no-misused-new': "error";
                    '@typescript-eslint/no-misused-promises': "error";
                    '@typescript-eslint/no-misused-spread': "error";
                    '@typescript-eslint/no-mixed-enums': "error";
                    '@typescript-eslint/no-namespace': "error";
                    '@typescript-eslint/no-non-null-asserted-nullish-coalescing': "error";
                    '@typescript-eslint/no-non-null-asserted-optional-chain': "error";
                    '@typescript-eslint/no-non-null-assertion': "error";
                    '@typescript-eslint/no-redundant-type-constituents': "error";
                    '@typescript-eslint/no-require-imports': "error";
                    '@typescript-eslint/no-this-alias': "error";
                    '@typescript-eslint/no-unnecessary-boolean-literal-compare': "error";
                    '@typescript-eslint/no-unnecessary-condition': "error";
                    '@typescript-eslint/no-unnecessary-template-expression': "error";
                    '@typescript-eslint/no-unnecessary-type-arguments': "error";
                    '@typescript-eslint/no-unnecessary-type-assertion': "error";
                    '@typescript-eslint/no-unnecessary-type-constraint': "error";
                    '@typescript-eslint/no-unnecessary-type-parameters': "error";
                    '@typescript-eslint/no-unsafe-argument': "error";
                    '@typescript-eslint/no-unsafe-assignment': "error";
                    '@typescript-eslint/no-unsafe-call': "error";
                    '@typescript-eslint/no-unsafe-declaration-merging': "error";
                    '@typescript-eslint/no-unsafe-enum-comparison': "error";
                    '@typescript-eslint/no-unsafe-function-type': "error";
                    '@typescript-eslint/no-unsafe-member-access': "error";
                    '@typescript-eslint/no-unsafe-return': "error";
                    '@typescript-eslint/no-unsafe-unary-minus': "error";
                    'no-unused-expressions': "off";
                    '@typescript-eslint/no-unused-expressions': "error";
                    'no-unused-vars': "off";
                    '@typescript-eslint/no-unused-vars': "error";
                    'no-useless-constructor': "off";
                    '@typescript-eslint/no-useless-constructor': "error";
                    '@typescript-eslint/no-wrapper-object-types': "error";
                    'no-throw-literal': "off";
                    '@typescript-eslint/only-throw-error': "error";
                    '@typescript-eslint/prefer-as-const': "error";
                    '@typescript-eslint/prefer-literal-enum-member': "error";
                    '@typescript-eslint/prefer-namespace-keyword': "error";
                    'prefer-promise-reject-errors': "off";
                    '@typescript-eslint/prefer-promise-reject-errors': "error";
                    '@typescript-eslint/prefer-reduce-type-parameter': "error";
                    '@typescript-eslint/prefer-return-this-type': "error";
                    '@typescript-eslint/related-getter-setter-pairs': "error";
                    'require-await': "off";
                    '@typescript-eslint/require-await': "error";
                    '@typescript-eslint/restrict-plus-operands': ["error", {
                        allowAny: boolean;
                        allowBoolean: boolean;
                        allowNullish: boolean;
                        allowNumberAndString: boolean;
                        allowRegExp: boolean;
                    }];
                    '@typescript-eslint/restrict-template-expressions': ["error", {
                        allowAny: boolean;
                        allowBoolean: boolean;
                        allowNever: boolean;
                        allowNullish: boolean;
                        allowNumber: boolean;
                        allowRegExp: boolean;
                    }];
                    'no-return-await': "off";
                    '@typescript-eslint/return-await': ["error", string];
                    '@typescript-eslint/triple-slash-reference': "error";
                    '@typescript-eslint/unbound-method': "error";
                    '@typescript-eslint/unified-signatures': "error";
                    '@typescript-eslint/use-unknown-in-catch-callback-variable': "error";
                };
            };
            'strict-type-checked-only': {
                extends: string[];
                rules: {
                    '@typescript-eslint/await-thenable': "error";
                    '@typescript-eslint/no-array-delete': "error";
                    '@typescript-eslint/no-base-to-string': "error";
                    '@typescript-eslint/no-confusing-void-expression': "error";
                    '@typescript-eslint/no-deprecated': "error";
                    '@typescript-eslint/no-duplicate-type-constituents': "error";
                    '@typescript-eslint/no-floating-promises': "error";
                    '@typescript-eslint/no-for-in-array': "error";
                    'no-implied-eval': "off";
                    '@typescript-eslint/no-implied-eval': "error";
                    '@typescript-eslint/no-meaningless-void-operator': "error";
                    '@typescript-eslint/no-misused-promises': "error";
                    '@typescript-eslint/no-misused-spread': "error";
                    '@typescript-eslint/no-mixed-enums': "error";
                    '@typescript-eslint/no-redundant-type-constituents': "error";
                    '@typescript-eslint/no-unnecessary-boolean-literal-compare': "error";
                    '@typescript-eslint/no-unnecessary-condition': "error";
                    '@typescript-eslint/no-unnecessary-template-expression': "error";
                    '@typescript-eslint/no-unnecessary-type-arguments': "error";
                    '@typescript-eslint/no-unnecessary-type-assertion': "error";
                    '@typescript-eslint/no-unnecessary-type-parameters': "error";
                    '@typescript-eslint/no-unsafe-argument': "error";
                    '@typescript-eslint/no-unsafe-assignment': "error";
                    '@typescript-eslint/no-unsafe-call': "error";
                    '@typescript-eslint/no-unsafe-enum-comparison': "error";
                    '@typescript-eslint/no-unsafe-member-access': "error";
                    '@typescript-eslint/no-unsafe-return': "error";
                    '@typescript-eslint/no-unsafe-unary-minus': "error";
                    'no-throw-literal': "off";
                    '@typescript-eslint/only-throw-error': "error";
                    'prefer-promise-reject-errors': "off";
                    '@typescript-eslint/prefer-promise-reject-errors': "error";
                    '@typescript-eslint/prefer-reduce-type-parameter': "error";
                    '@typescript-eslint/prefer-return-this-type': "error";
                    '@typescript-eslint/related-getter-setter-pairs': "error";
                    'require-await': "off";
                    '@typescript-eslint/require-await': "error";
                    '@typescript-eslint/restrict-plus-operands': ["error", {
                        allowAny: boolean;
                        allowBoolean: boolean;
                        allowNullish: boolean;
                        allowNumberAndString: boolean;
                        allowRegExp: boolean;
                    }];
                    '@typescript-eslint/restrict-template-expressions': ["error", {
                        allowAny: boolean;
                        allowBoolean: boolean;
                        allowNever: boolean;
                        allowNullish: boolean;
                        allowNumber: boolean;
                        allowRegExp: boolean;
                    }];
                    'no-return-await': "off";
                    '@typescript-eslint/return-await': ["error", string];
                    '@typescript-eslint/unbound-method': "error";
                    '@typescript-eslint/use-unknown-in-catch-callback-variable': "error";
                };
            };
            stylistic: {
                extends: string[];
                rules: {
                    '@typescript-eslint/adjacent-overload-signatures': "error";
                    '@typescript-eslint/array-type': "error";
                    '@typescript-eslint/ban-tslint-comment': "error";
                    '@typescript-eslint/class-literal-property-style': "error";
                    '@typescript-eslint/consistent-generic-constructors': "error";
                    '@typescript-eslint/consistent-indexed-object-style': "error";
                    '@typescript-eslint/consistent-type-assertions': "error";
                    '@typescript-eslint/consistent-type-definitions': "error";
                    '@typescript-eslint/no-confusing-non-null-assertion': "error";
                    'no-empty-function': "off";
                    '@typescript-eslint/no-empty-function': "error";
                    '@typescript-eslint/no-inferrable-types': "error";
                    '@typescript-eslint/prefer-for-of': "error";
                    '@typescript-eslint/prefer-function-type': "error";
                };
            };
            'stylistic-type-checked': {
                extends: string[];
                rules: {
                    '@typescript-eslint/adjacent-overload-signatures': "error";
                    '@typescript-eslint/array-type': "error";
                    '@typescript-eslint/ban-tslint-comment': "error";
                    '@typescript-eslint/class-literal-property-style': "error";
                    '@typescript-eslint/consistent-generic-constructors': "error";
                    '@typescript-eslint/consistent-indexed-object-style': "error";
                    '@typescript-eslint/consistent-type-assertions': "error";
                    '@typescript-eslint/consistent-type-definitions': "error";
                    'dot-notation': "off";
                    '@typescript-eslint/dot-notation': "error";
                    '@typescript-eslint/no-confusing-non-null-assertion': "error";
                    'no-empty-function': "off";
                    '@typescript-eslint/no-empty-function': "error";
                    '@typescript-eslint/no-inferrable-types': "error";
                    '@typescript-eslint/non-nullable-type-assertion-style': "error";
                    '@typescript-eslint/prefer-find': "error";
                    '@typescript-eslint/prefer-for-of': "error";
                    '@typescript-eslint/prefer-function-type': "error";
                    '@typescript-eslint/prefer-includes': "error";
                    '@typescript-eslint/prefer-nullish-coalescing': "error";
                    '@typescript-eslint/prefer-optional-chain': "error";
                    '@typescript-eslint/prefer-regexp-exec': "error";
                    '@typescript-eslint/prefer-string-starts-ends-with': "error";
                };
            };
            'stylistic-type-checked-only': {
                extends: string[];
                rules: {
                    'dot-notation': "off";
                    '@typescript-eslint/dot-notation': "error";
                    '@typescript-eslint/non-nullable-type-assertion-style': "error";
                    '@typescript-eslint/prefer-find': "error";
                    '@typescript-eslint/prefer-includes': "error";
                    '@typescript-eslint/prefer-nullish-coalescing': "error";
                    '@typescript-eslint/prefer-optional-chain': "error";
                    '@typescript-eslint/prefer-regexp-exec': "error";
                    '@typescript-eslint/prefer-string-starts-ends-with': "error";
                };
            };
        };
        meta: {
            name: string;
            version: string;
        };
        rules: {
            'adjacent-overload-signatures': TSESLint.RuleModule<"adjacentSignature", [], import("../rules").ESLintPluginDocs, TSESLint.RuleListener>;
            'array-type': TSESLint.RuleModule<import("./rules/array-type").MessageIds, import("./rules/array-type").Options, import("../rules").ESLintPluginDocs, TSESLint.RuleListener>;
            'await-thenable': TSESLint.RuleModule<import("./rules/await-thenable").MessageId, [], import("../rules").ESLintPluginDocs, TSESLint.RuleListener>;
            'ban-ts-comment': TSESLint.RuleModule<import("./rules/ban-ts-comment").MessageIds, import("./rules/ban-ts-comment").Options, import("../rules").ESLintPluginDocs, TSESLint.RuleListener>;
            'ban-tslint-comment': TSESLint.RuleModule<"commentDetected", [], import("../rules").ESLintPluginDocs, TSESLint.RuleListener>;
            'class-literal-property-style': TSESLint.RuleModule<import("./rules/class-literal-property-style").MessageIds, import("./rules/class-literal-property-style").Options, import("../rules").ESLintPluginDocs, TSESLint.RuleListener>;
            'class-methods-use-this': TSESLint.RuleModule<"missingThis", import("./rules/class-methods-use-this").Options, import("../rules").ESLintPluginDocs, TSESLint.RuleListener>;
            'consistent-generic-constructors': TSESLint.RuleModule<import("./rules/consistent-generic-constructors").MessageIds, import("./rules/consistent-generic-constructors").Options, import("../rules").ESLintPluginDocs, TSESLint.RuleListener>;
            'consistent-indexed-object-style': TSESLint.RuleModule<import("./rules/consistent-indexed-object-style").MessageIds, import("./rules/consistent-indexed-object-style").Options, import("../rules").ESLintPluginDocs, TSESLint.RuleListener>;
            'consistent-return': TSESLint.RuleModule<"missingReturn" | "missingReturnValue" | "unexpectedReturnValue", [({
                treatUndefinedAsUnspecified?: boolean;
            } | undefined)?], import("../rules").ESLintPluginDocs, TSESLint.RuleListener>;
            'consistent-type-assertions': TSESLint.RuleModule<import("./rules/consistent-type-assertions").MessageIds, import("./rules/consistent-type-assertions").Options, import("../rules").ESLintPluginDocs, TSESLint.RuleListener>;
            'consistent-type-definitions': TSESLint.RuleModule<"interfaceOverType" | "typeOverInterface", [string], import("../rules").ESLintPluginDocs, TSESLint.RuleListener>;
            'consistent-type-exports': TSESLint.RuleModule<import("./rules/consistent-type-exports").MessageIds, import("./rules/consistent-type-exports").Options, import("../rules").ESLintPluginDocs, TSESLint.RuleListener>;
            'consistent-type-imports': TSESLint.RuleModule<import("./rules/consistent-type-imports").MessageIds, import("./rules/consistent-type-imports").Options, import("../rules").ESLintPluginDocs, TSESLint.RuleListener>;
            'default-param-last': TSESLint.RuleModule<"shouldBeLast", [], import("../rules").ESLintPluginDocs, TSESLint.RuleListener>;
            'dot-notation': TSESLint.RuleModule<"useBrackets" | "useDot", [{
                allowIndexSignaturePropertyAccess?: boolean;
                allowKeywords?: boolean;
                allowPattern?: string;
                allowPrivateClassPropertyAccess?: boolean;
                allowProtectedClassPropertyAccess?: boolean;
            }], import("../rules").ESLintPluginDocs, TSESLint.RuleListener>;
            'explicit-function-return-type': TSESLint.RuleModule<"missingReturnType", import("./rules/explicit-function-return-type").Options, import("../rules").ESLintPluginDocs, TSESLint.RuleListener>;
            'explicit-member-accessibility': TSESLint.RuleModule<import("./rules/explicit-member-accessibility").MessageIds, import("./rules/explicit-member-accessibility").Options, import("../rules").ESLintPluginDocs, TSESLint.RuleListener>;
            'explicit-module-boundary-types': TSESLint.RuleModule<import("./rules/explicit-module-boundary-types").MessageIds, import("./rules/explicit-module-boundary-types").Options, import("../rules").ESLintPluginDocs, TSESLint.RuleListener>;
            'init-declarations': TSESLint.RuleModule<"initialized" | "notInitialized", ["always" | "never", ({
                ignoreForLoopInit?: boolean;
            } | undefined)?], import("../rules").ESLintPluginDocs, TSESLint.RuleListener>;
            'max-params': TSESLint.RuleModule<"exceed", ({
                countVoidThis?: boolean;
                max: number;
            } | {
                countVoidThis?: boolean;
                maximum: number;
            })[], import("../rules").ESLintPluginDocs, TSESLint.RuleListener>;
            'member-ordering': TSESLint.RuleModule<import("./rules/member-ordering").MessageIds, import("./rules/member-ordering").Options, import("../rules").ESLintPluginDocs, TSESLint.RuleListener>;
            'method-signature-style': TSESLint.RuleModule<import("./rules/method-signature-style").MessageIds, import("./rules/method-signature-style").Options, import("../rules").ESLintPluginDocs, TSESLint.RuleListener>;
            'naming-convention': TSESLint.RuleModule<import("./rules/naming-convention").MessageIds, import("./rules/naming-convention").Options, import("../rules").ESLintPluginDocs, TSESLint.RuleListener>;
            'no-array-constructor': TSESLint.RuleModule<"useLiteral", [], import("../rules").ESLintPluginDocs, TSESLint.RuleListener>;
            'no-array-delete': TSESLint.RuleModule<import("./rules/no-array-delete").MessageId, [], import("../rules").ESLintPluginDocs, TSESLint.RuleListener>;
            'no-base-to-string': TSESLint.RuleModule<import("./rules/no-base-to-string").MessageIds, import("./rules/no-base-to-string").Options, import("../rules").ESLintPluginDocs, TSESLint.RuleListener>;
            'no-confusing-non-null-assertion': TSESLint.RuleModule<import("./rules/no-confusing-non-null-assertion").MessageId, [], import("../rules").ESLintPluginDocs, TSESLint.RuleListener>;
            'no-confusing-void-expression': TSESLint.RuleModule<import("./rules/no-confusing-void-expression").MessageId, import("./rules/no-confusing-void-expression").Options, import("../rules").ESLintPluginDocs, TSESLint.RuleListener>;
            'no-deprecated': TSESLint.RuleModule<"deprecated" | "deprecatedWithReason", [{
                allow?: import("@typescript-eslint/type-utils").TypeOrValueSpecifier[];
            }], import("../rules").ESLintPluginDocs, TSESLint.RuleListener>;
            'no-dupe-class-members': TSESLint.RuleModule<"unexpected", [], import("../rules").ESLintPluginDocs, TSESLint.RuleListener>;
            'no-duplicate-enum-values': TSESLint.RuleModule<"duplicateValue", [], import("../rules").ESLintPluginDocs, TSESLint.RuleListener>;
            'no-duplicate-type-constituents': TSESLint.RuleModule<import("./rules/no-duplicate-type-constituents").MessageIds, import("./rules/no-duplicate-type-constituents").Options, import("../rules").ESLintPluginDocs, TSESLint.RuleListener>;
            'no-dynamic-delete': TSESLint.RuleModule<"dynamicDelete", [], import("../rules").ESLintPluginDocs, TSESLint.RuleListener>;
            'no-empty-function': TSESLint.RuleModule<"unexpected", [{
                allow?: string[];
            }], import("../rules").ESLintPluginDocs, TSESLint.RuleListener>;
            'no-empty-interface': TSESLint.RuleModule<import("./rules/no-empty-interface").MessageIds, import("./rules/no-empty-interface").Options, import("../rules").ESLintPluginDocs, TSESLint.RuleListener>;
            'no-empty-object-type': TSESLint.RuleModule<import("./rules/no-empty-object-type").MessageIds, import("./rules/no-empty-object-type").Options, import("../rules").ESLintPluginDocs, TSESLint.RuleListener>;
            'no-explicit-any': TSESLint.RuleModule<import("./rules/no-explicit-any").MessageIds, import("./rules/no-explicit-any").Options, import("../rules").ESLintPluginDocs, TSESLint.RuleListener>;
            'no-extra-non-null-assertion': TSESLint.RuleModule<"noExtraNonNullAssertion", [], import("../rules").ESLintPluginDocs, TSESLint.RuleListener>;
            'no-extraneous-class': TSESLint.RuleModule<import("./rules/no-extraneous-class").MessageIds, import("./rules/no-extraneous-class").Options, import("../rules").ESLintPluginDocs, TSESLint.RuleListener>;
            'no-floating-promises': TSESLint.RuleModule<import("./rules/no-floating-promises").MessageId, import("./rules/no-floating-promises").Options, import("../rules").ESLintPluginDocs, TSESLint.RuleListener>;
            'no-for-in-array': TSESLint.RuleModule<"forInViolation", [], import("../rules").ESLintPluginDocs, TSESLint.RuleListener>;
            'no-implied-eval': TSESLint.RuleModule<"noFunctionConstructor" | "noImpliedEvalError", [], import("../rules").ESLintPluginDocs, TSESLint.RuleListener>;
            'no-import-type-side-effects': TSESLint.RuleModule<"useTopLevelQualifier", [], import("../rules").ESLintPluginDocs, TSESLint.RuleListener>;
            'no-inferrable-types': TSESLint.RuleModule<"noInferrableType", import("./rules/no-inferrable-types").Options, import("../rules").ESLintPluginDocs, TSESLint.RuleListener>;
            'no-invalid-this': TSESLint.RuleModule<"unexpectedThis", [({
                capIsConstructor?: boolean;
            } | undefined)?], import("../rules").ESLintPluginDocs, TSESLint.RuleListener>;
            'no-invalid-void-type': TSESLint.RuleModule<import("./rules/no-invalid-void-type").MessageIds, [import("./rules/no-invalid-void-type").Options], import("../rules").ESLintPluginDocs, TSESLint.RuleListener>;
            'no-loop-func': TSESLint.RuleModule<"unsafeRefs", [], import("../rules").ESLintPluginDocs, TSESLint.RuleListener>;
            'no-loss-of-precision': TSESLint.RuleModule<"noLossOfPrecision", [], import("../rules").ESLintPluginDocs, TSESLint.RuleListener>;
            'no-magic-numbers': TSESLint.RuleModule<"noMagic", [{
                detectObjects?: boolean;
                enforceConst?: boolean;
                ignore?: (number | string)[];
                ignoreArrayIndexes?: boolean;
                ignoreEnums?: boolean;
                ignoreNumericLiteralTypes?: boolean;
                ignoreReadonlyClassProperties?: boolean;
                ignoreTypeIndexes?: boolean;
            }], import("../rules").ESLintPluginDocs, TSESLint.RuleListener>;
            'no-meaningless-void-operator': TSESLint.RuleModule<"meaninglessVoidOperator" | "removeVoid", import("./rules/no-meaningless-void-operator").Options, import("../rules").ESLintPluginDocs, TSESLint.RuleListener>;
            'no-misused-new': TSESLint.RuleModule<"errorMessageClass" | "errorMessageInterface", [], import("../rules").ESLintPluginDocs, TSESLint.RuleListener>;
            'no-misused-promises': TSESLint.RuleModule<import("./rules/no-misused-promises").MessageId, import("./rules/no-misused-promises").Options, import("../rules").ESLintPluginDocs, TSESLint.RuleListener>;
            'no-misused-spread': TSESLint.RuleModule<"addAwait" | "noArraySpreadInObject" | "noClassDeclarationSpreadInObject" | "noClassInstanceSpreadInObject" | "noFunctionSpreadInObject" | "noIterableSpreadInObject" | "noMapSpreadInObject" | "noPromiseSpreadInObject" | "noStringSpread" | "replaceMapSpreadInObject", [{
                allow?: import("@typescript-eslint/type-utils").TypeOrValueSpecifier[];
            }], import("../rules").ESLintPluginDocs, TSESLint.RuleListener>;
            'no-mixed-enums': TSESLint.RuleModule<"mixed", [], import("../rules").ESLintPluginDocs, TSESLint.RuleListener>;
            'no-namespace': TSESLint.RuleModule<"moduleSyntaxIsPreferred", import("./rules/no-namespace").Options, import("../rules").ESLintPluginDocs, TSESLint.RuleListener>;
            'no-non-null-asserted-nullish-coalescing': TSESLint.RuleModule<"noNonNullAssertedNullishCoalescing" | "suggestRemovingNonNull", [], import("../rules").ESLintPluginDocs, TSESLint.RuleListener>;
            'no-non-null-asserted-optional-chain': TSESLint.RuleModule<"suggestRemovingNonNull" | "noNonNullOptionalChain", [], import("../rules").ESLintPluginDocs, TSESLint.RuleListener>;
            'no-non-null-assertion': TSESLint.RuleModule<import("./rules/no-non-null-assertion").MessageIds, [], import("../rules").ESLintPluginDocs, TSESLint.RuleListener>;
            'no-redeclare': TSESLint.RuleModule<import("./rules/no-redeclare").MessageIds, import("./rules/no-redeclare").Options, import("../rules").ESLintPluginDocs, TSESLint.RuleListener>;
            'no-redundant-type-constituents': TSESLint.RuleModule<"overrides" | "errorTypeOverrides" | "literalOverridden" | "overridden" | "primitiveOverridden", [], import("../rules").ESLintPluginDocs, TSESLint.RuleListener>;
            'no-require-imports': TSESLint.RuleModule<"noRequireImports", import("./rules/no-require-imports").Options, import("../rules").ESLintPluginDocs, TSESLint.RuleListener>;
            'no-restricted-imports': TSESLint.RuleModule<"everything" | "everythingWithCustomMessage" | "importName" | "importNameWithCustomMessage" | "path" | "pathWithCustomMessage" | "patterns" | "patternWithCustomMessage", [import("eslint/lib/rules/no-restricted-imports").ObjectOfPathsAndPatterns] | import("eslint/lib/rules/no-restricted-imports").ArrayOfStringOrObject, import("../rules").ESLintPluginDocs, TSESLint.RuleListener>;
            'no-restricted-types': TSESLint.RuleModule<import("./rules/no-restricted-types").MessageIds, import("./rules/no-restricted-types").Options, import("../rules").ESLintPluginDocs, TSESLint.RuleListener>;
            'no-shadow': TSESLint.RuleModule<import("./rules/no-shadow").MessageIds, import("./rules/no-shadow").Options, import("../rules").ESLintPluginDocs, TSESLint.RuleListener>;
            'no-this-alias': TSESLint.RuleModule<import("./rules/no-this-alias").MessageIds, import("./rules/no-this-alias").Options, import("../rules").ESLintPluginDocs, TSESLint.RuleListener>;
            'no-type-alias': TSESLint.RuleModule<import("./rules/no-type-alias").MessageIds, import("./rules/no-type-alias").Options, import("../rules").ESLintPluginDocs, TSESLint.RuleListener>;
            'no-unnecessary-boolean-literal-compare': TSESLint.RuleModule<import("./rules/no-unnecessary-boolean-literal-compare").MessageIds, import("./rules/no-unnecessary-boolean-literal-compare").Options, import("../rules").ESLintPluginDocs, TSESLint.RuleListener>;
            'no-unnecessary-condition': TSESLint.RuleModule<import("./rules/no-unnecessary-condition").MessageId, import("./rules/no-unnecessary-condition").Options, import("../rules").ESLintPluginDocs, TSESLint.RuleListener>;
            'no-unnecessary-parameter-property-assignment': TSESLint.RuleModule<"unnecessaryAssign", [], import("../rules").ESLintPluginDocs, TSESLint.RuleListener>;
            'no-unnecessary-qualifier': TSESLint.RuleModule<"unnecessaryQualifier", [], import("../rules").ESLintPluginDocs, TSESLint.RuleListener>;
            'no-unnecessary-template-expression': TSESLint.RuleModule<"noUnnecessaryTemplateExpression", [], import("../rules").ESLintPluginDocs, TSESLint.RuleListener>;
            'no-unnecessary-type-arguments': TSESLint.RuleModule<"unnecessaryTypeParameter", [], import("../rules").ESLintPluginDocs, TSESLint.RuleListener>;
            'no-unnecessary-type-assertion': TSESLint.RuleModule<import("./rules/no-unnecessary-type-assertion").MessageIds, import("./rules/no-unnecessary-type-assertion").Options, import("../rules").ESLintPluginDocs, TSESLint.RuleListener>;
            'no-unnecessary-type-constraint': TSESLint.RuleModule<"removeUnnecessaryConstraint" | "unnecessaryConstraint", [], import("../rules").ESLintPluginDocs, TSESLint.RuleListener>;
            'no-unnecessary-type-conversion': TSESLint.RuleModule<"suggestRemove" | "suggestSatisfies" | "unnecessaryTypeConversion", [], import("../rules").ESLintPluginDocs, TSESLint.RuleListener>;
            'no-unnecessary-type-parameters': TSESLint.RuleModule<"replaceUsagesWithConstraint" | "sole", [], import("../rules").ESLintPluginDocs, TSESLint.RuleListener>;
            'no-unsafe-argument': TSESLint.RuleModule<import("./rules/no-unsafe-argument").MessageIds, [], import("../rules").ESLintPluginDocs, TSESLint.RuleListener>;
            'no-unsafe-assignment': TSESLint.RuleModule<"unsafeArraySpread" | "anyAssignment" | "anyAssignmentThis" | "unsafeArrayPattern" | "unsafeArrayPatternFromTuple" | "unsafeAssignment", [], import("../rules").ESLintPluginDocs, TSESLint.RuleListener>;
            'no-unsafe-call': TSESLint.RuleModule<import("./rules/no-unsafe-call").MessageIds, [], import("../rules").ESLintPluginDocs, TSESLint.RuleListener>;
            'no-unsafe-declaration-merging': TSESLint.RuleModule<"unsafeMerging", [], import("../rules").ESLintPluginDocs, TSESLint.RuleListener>;
            'no-unsafe-enum-comparison': TSESLint.RuleModule<"mismatchedCase" | "mismatchedCondition" | "replaceValueWithEnum", [], import("../rules").ESLintPluginDocs, TSESLint.RuleListener>;
            'no-unsafe-function-type': TSESLint.RuleModule<"bannedFunctionType", [], import("../rules").ESLintPluginDocs, TSESLint.RuleListener>;
            'no-unsafe-member-access': TSESLint.RuleModule<"unsafeComputedMemberAccess" | "unsafeMemberExpression" | "unsafeThisMemberExpression", [], import("../rules").ESLintPluginDocs, TSESLint.RuleListener>;
            'no-unsafe-return': TSESLint.RuleModule<"unsafeReturn" | "unsafeReturnAssignment" | "unsafeReturnThis", [], import("../rules").ESLintPluginDocs, TSESLint.RuleListener>;
            'no-unsafe-type-assertion': TSESLint.RuleModule<"unsafeOfAnyTypeAssertion" | "unsafeToAnyTypeAssertion" | "unsafeToUnconstrainedTypeAssertion" | "unsafeTypeAssertion" | "unsafeTypeAssertionAssignableToConstraint", [], import("../rules").ESLintPluginDocs, TSESLint.RuleListener>;
            'no-unsafe-unary-minus': TSESLint.RuleModule<"unaryMinus", [], import("../rules").ESLintPluginDocs, TSESLint.RuleListener>;
            'no-unused-expressions': TSESLint.RuleModule<"expected", [{
                allowShortCircuit?: boolean;
                allowTaggedTemplates?: boolean;
                allowTernary?: boolean;
            }], import("../rules").ESLintPluginDocs, TSESLint.RuleListener>;
            'no-unused-vars': TSESLint.RuleModule<import("./rules/no-unused-vars").MessageIds, import("./rules/no-unused-vars").Options, import("../rules").ESLintPluginDocs, TSESLint.RuleListener>;
            'no-use-before-define': TSESLint.RuleModule<"noUseBeforeDefine", import("./rules/no-use-before-define").Options, import("../rules").ESLintPluginDocs, TSESLint.RuleListener>;
            'no-useless-constructor': TSESLint.RuleModule<"noUselessConstructor" | "removeConstructor", [], import("../rules").ESLintPluginDocs, TSESLint.RuleListener>;
            'no-useless-empty-export': TSESLint.RuleModule<"uselessExport", [], import("../rules").ESLintPluginDocs, TSESLint.RuleListener>;
            'no-var-requires': TSESLint.RuleModule<"noVarReqs", import("./rules/no-var-requires").Options, import("../rules").ESLintPluginDocs, TSESLint.RuleListener>;
            'no-wrapper-object-types': TSESLint.RuleModule<"bannedClassType", [], import("../rules").ESLintPluginDocs, TSESLint.RuleListener>;
            'non-nullable-type-assertion-style': TSESLint.RuleModule<"preferNonNullAssertion", [], import("../rules").ESLintPluginDocs, TSESLint.RuleListener>;
            'only-throw-error': TSESLint.RuleModule<import("./rules/only-throw-error").MessageIds, import("./rules/only-throw-error").Options, import("../rules").ESLintPluginDocs, TSESLint.RuleListener>;
            'parameter-properties': TSESLint.RuleModule<import("./rules/parameter-properties").MessageIds, import("./rules/parameter-properties").Options, import("../rules").ESLintPluginDocs, TSESLint.RuleListener>;
            'prefer-as-const': TSESLint.RuleModule<"preferConstAssertion" | "variableConstAssertion" | "variableSuggest", [], import("../rules").ESLintPluginDocs, TSESLint.RuleListener>;
            'prefer-destructuring': TSESLint.RuleModule<"preferDestructuring", import("./rules/prefer-destructuring").Options, import("../rules").ESLintPluginDocs, TSESLint.RuleListener>;
            'prefer-enum-initializers': TSESLint.RuleModule<import("./rules/prefer-enum-initializers").MessageIds, [], import("../rules").ESLintPluginDocs, TSESLint.RuleListener>;
            'prefer-find': TSESLint.RuleModule<"preferFind" | "preferFindSuggestion", [], import("../rules").ESLintPluginDocs, TSESLint.RuleListener>;
            'prefer-for-of': TSESLint.RuleModule<"preferForOf", [], import("../rules").ESLintPluginDocs, TSESLint.RuleListener>;
            'prefer-function-type': TSESLint.RuleModule<"functionTypeOverCallableType" | "unexpectedThisOnFunctionOnlyInterface", [], import("../rules").ESLintPluginDocs, TSESLint.RuleListener>;
            'prefer-includes': TSESLint.RuleModule<"preferIncludes" | "preferStringIncludes", [], import("../rules").ESLintPluginDocs, TSESLint.RuleListener>;
            'prefer-literal-enum-member': TSESLint.RuleModule<"notLiteral" | "notLiteralOrBitwiseExpression", [{
                allowBitwiseExpressions: boolean;
            }], import("../rules").ESLintPluginDocs, TSESLint.RuleListener>;
            'prefer-namespace-keyword': TSESLint.RuleModule<"useNamespace", [], import("../rules").ESLintPluginDocs, TSESLint.RuleListener>;
            'prefer-nullish-coalescing': TSESLint.RuleModule<import("./rules/prefer-nullish-coalescing").MessageIds, import("./rules/prefer-nullish-coalescing").Options, import("../rules").ESLintPluginDocs, TSESLint.RuleListener>;
            'prefer-optional-chain': TSESLint.RuleModule<import("./rules/prefer-optional-chain-utils/PreferOptionalChainOptions").PreferOptionalChainMessageIds, [import("./rules/prefer-optional-chain-utils/PreferOptionalChainOptions").PreferOptionalChainOptions], import("../rules").ESLintPluginDocs, TSESLint.RuleListener>;
            'prefer-promise-reject-errors': TSESLint.RuleModule<"rejectAnError", import("./rules/prefer-promise-reject-errors").Options, import("../rules").ESLintPluginDocs, TSESLint.RuleListener>;
            'prefer-readonly': TSESLint.RuleModule<"preferReadonly", import("./rules/prefer-readonly").Options, import("../rules").ESLintPluginDocs, TSESLint.RuleListener>;
            'prefer-readonly-parameter-types': TSESLint.RuleModule<"shouldBeReadonly", import("./rules/prefer-readonly-parameter-types").Options, import("../rules").ESLintPluginDocs, TSESLint.RuleListener>;
            'prefer-reduce-type-parameter': TSESLint.RuleModule<"preferTypeParameter", [], import("../rules").ESLintPluginDocs, TSESLint.RuleListener>;
            'prefer-regexp-exec': TSESLint.RuleModule<"regExpExecOverStringMatch", [], import("../rules").ESLintPluginDocs, TSESLint.RuleListener>;
            'prefer-return-this-type': TSESLint.RuleModule<"useThisType", [], import("../rules").ESLintPluginDocs, TSESLint.RuleListener>;
            'prefer-string-starts-ends-with': TSESLint.RuleModule<import("./rules/prefer-string-starts-ends-with").MessageIds, import("./rules/prefer-string-starts-ends-with").Options, import("../rules").ESLintPluginDocs, TSESLint.RuleListener>;
            'prefer-ts-expect-error': TSESLint.RuleModule<"preferExpectErrorComment", [], import("../rules").ESLintPluginDocs, TSESLint.RuleListener>;
            'promise-function-async': TSESLint.RuleModule<import("./rules/promise-function-async").MessageIds, import("./rules/promise-function-async").Options, import("../rules").ESLintPluginDocs, TSESLint.RuleListener>;
            'related-getter-setter-pairs': TSESLint.RuleModule<"mismatch", [], import("../rules").ESLintPluginDocs, TSESLint.RuleListener>;
            'require-array-sort-compare': TSESLint.RuleModule<"requireCompare", import("./rules/require-array-sort-compare").Options, import("../rules").ESLintPluginDocs, TSESLint.RuleListener>;
            'require-await': TSESLint.RuleModule<"missingAwait" | "removeAsync", [], import("../rules").ESLintPluginDocs, TSESLint.RuleListener>;
            'restrict-plus-operands': TSESLint.RuleModule<import("./rules/restrict-plus-operands").MessageIds, import("./rules/restrict-plus-operands").Options, import("../rules").ESLintPluginDocs, TSESLint.RuleListener>;
            'restrict-template-expressions': TSESLint.RuleModule<"invalidType", import("./rules/restrict-template-expressions").Options, import("../rules").ESLintPluginDocs, TSESLint.RuleListener>;
            'return-await': TSESLint.RuleModule<"disallowedPromiseAwait" | "disallowedPromiseAwaitSuggestion" | "nonPromiseAwait" | "requiredPromiseAwait" | "requiredPromiseAwaitSuggestion", [string], import("../rules").ESLintPluginDocs, TSESLint.RuleListener>;
            'sort-type-constituents': TSESLint.RuleModule<import("./rules/sort-type-constituents").MessageIds, import("./rules/sort-type-constituents").Options, import("../rules").ESLintPluginDocs, TSESLint.RuleListener>;
            'strict-boolean-expressions': TSESLint.RuleModule<import("./rules/strict-boolean-expressions").MessageId, import("./rules/strict-boolean-expressions").Options, import("../rules").ESLintPluginDocs, TSESLint.RuleListener>;
            'switch-exhaustiveness-check': TSESLint.RuleModule<import("./rules/switch-exhaustiveness-check").MessageIds, import("./rules/switch-exhaustiveness-check").Options, import("../rules").ESLintPluginDocs, TSESLint.RuleListener>;
            'triple-slash-reference': TSESLint.RuleModule<"tripleSlashReference", import("./rules/triple-slash-reference").Options, import("../rules").ESLintPluginDocs, TSESLint.RuleListener>;
            typedef: TSESLint.RuleModule<import("./rules/typedef").MessageIds, import("./rules/typedef").Options, import("../rules").ESLintPluginDocs, TSESLint.RuleListener>;
            'unbound-method': TSESLint.RuleModule<import("./rules/unbound-method").MessageIds, import("./rules/unbound-method").Options, import("../rules").ESLintPluginDocs, TSESLint.RuleListener>;
            'unified-signatures': TSESLint.RuleModule<import("./rules/unified-signatures").MessageIds, import("./rules/unified-signatures").Options, import("../rules").ESLintPluginDocs, TSESLint.RuleListener>;
            'use-unknown-in-catch-callback-variable': TSESLint.RuleModule<import("./rules/use-unknown-in-catch-callback-variable").MessageIds, [], import("../rules").ESLintPluginDocs, TSESLint.RuleListener>;
        };
    };
};
export = _default;
//# sourceMappingURL=raw-plugin.d.ts.map